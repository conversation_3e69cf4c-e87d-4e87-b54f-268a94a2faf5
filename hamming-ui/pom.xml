<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <artifactId>hamming-parent</artifactId>
    <groupId>com.topsec.hamming</groupId>
    <version>3.5.0-SNAPSHOT</version>
  </parent>

  <artifactId>hamming-ui</artifactId>

  <name>Hamming UI</name>

  <build>
    <plugins>
      <plugin>
        <groupId>io.swagger.codegen.v3</groupId>
        <artifactId>swagger-codegen-maven-plugin</artifactId>
        <version>3.0.30</version>
        <executions>
          <execution>
            <id>angular</id>
            <goals>
              <goal>generate</goal>
            </goals>
            <configuration>
              <inputSpec>${project.basedir}/../hamming-client/src/main/swagger/alarmrule.json</inputSpec>
              <language>typescript-angular</language>
              <output>${project.basedir}/src/app/sdk</output>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-clean-plugin</artifactId>
        <version>3.1.0</version>
        <configuration>
          <filesets>
            <fileset>
              <directory>${project.basedir}/src/app/sdk</directory>
            </fileset>
          </filesets>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
