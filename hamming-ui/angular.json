{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false, "defaultCollection": "@angular-eslint/schematics"}, "version": 1, "newProjectRoot": "projects", "projects": {"hamming": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "less"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "options": {"customWebpackConfig": {"path": "./extra-webpack.config.js", "mergeStrategies": {"externals": "replace"}}, "baseHref": "/hamming/", "deployUrl": "/hamming/", "outputPath": "dist/hamming", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.less", {"bundleName": "tui-font", "input": "src/tui-font.less"}, {"bundleName": "tui", "input": "src/tui.less"}], "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": false, "budgets": [{"type": "initial", "maximumWarning": "20mb", "maximumError": "20mb"}]}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"development": {"browserTarget": "hamming:build:development"}}, "options": {"proxyConfig": "proxy.conf.json"}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "hamming:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": ["src/styles.less"], "scripts": [], "assets": ["src/favicon.ico", "src/assets"]}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts"]}}}}}, "defaultProject": "hamming"}