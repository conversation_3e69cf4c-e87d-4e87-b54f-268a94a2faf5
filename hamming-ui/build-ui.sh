desc=`git describe --tags`
result=$(echo $desc | grep "g")
if [ $result ]
then
    update=`echo $desc | awk -F "-" '{print $(NF-1)}'`
    tag=`echo $desc | awk -F "-" '{print $1}'`-`echo $desc | awk -F "-" '{print $2}'`
else
    update=0
    tag=$desc
fi
echo $update
echo $tag
sed -i 's/\"version\": \".*\",/\"version\": \"'${tag}'.'${update}'\",/g' package.json
#rm package-lock.json
rm -rf node_modules

npm config set registry http://10.11.6.81:8989/nexus/content/repositories/npm-all/
npm i
#npm run packagr
#cd dist
#npm publish -registry=http://10.11.6.81:8989/nexus/content/repositories/npm-repo/
#cd ..
npm run build
