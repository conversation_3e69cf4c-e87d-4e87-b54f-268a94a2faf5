@import url("~@tui/component-library/src/style/mixin/mixin.less");

@amount-frontIcon: var(--amount-frontIcon, #f85555);
@amount-frontIcon-bottom: var(--amount-frontIcon-bottom, #f68c86);
@amount-afterIcon-box: var(--amount-afterIcon-box, #ff7b7b);

@font-face {
  font-family: "DIN";
  src: url("./assets/fonts/DIN-Bold.otf") format("opentype");
}

// 应用操作栏
.handle-bar {
  min-width: 1160px;
  padding: 8px 10px 0;
  background-color: #f3f4f7;
}

.amount {
  display: flex;
  margin: 8px 0;

  compound-chart {
    display: flex;
    justify-content: space-between;
    width: 20%;
    height: 106px;
    margin-left: 8px;
    background: #fff;
    border: 1px solid #ebe9e9;
    border-radius: 2px;

    compound-chart {
      display: flex;
      align-items: center;
      justify-items: center;
      width: 100%;
    }
  }

  compound-chart:first-child {
    margin-left: 0;
  }
}

//对比数据统计指标部分
.amount-char {
  display: block;
  padding: 24px;
  margin: 8px 0;
  background: #fff;
  border: 1px solid #ebe9e9;

  .amount-title {
    display: flex;
    align-items: center;
    justify-content: space-between;

    p {
      margin-bottom: 18px;
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }
  }

  .amount-inner {
    display: flex;
    justify-content: space-between;

    img {
      max-width: 100%;
    }

    .amount-title {
      margin-bottom: 18px;
      font-size: 14px;
      color: #525252;
      text-indent: 20px;
      background-size: 15px 13px;
    }

    .amount-list {
      width: 25%;
      height: 200px;

      .pieChart {
        width: 100%;
        height: 120%;
        margin: -40px 0 0 -50px;
        overflow: hidden;
      }
    }

    .amount-bar {
      width: 51%;
      height: 200px;

      .barChart {
        width: 100%;
        height: 85%;
        overflow: hidden;
      }
    }
  }
}

//列表查询条件编辑部分
.trend-searchChart {
  width: 100%;
  padding: 10px 16px;
  margin-top: 8px;
  background: #fff;
  border: 1px solid #edeef1;

  .trend-selected {
    .trend-selected-title {
      height: 30px;
      margin-right: 6px;
      font-size: 14px;
      line-height: 30px;
      color: #5f6364;
    }

    .trend-selected-span {
      display: table;
    }
  }
}

// 列表页
.container-box {
  min-width: 1160px;
  padding: 0 10px 8px;
  background: #f3f4f7;
  //position: relative;
  //top: 0;
  //bottom: 0;
  //overflow: auto;
  // 查询模块
  .searchBox-box {
    width: 100%;
    padding: 24px 24px 0;
    margin: 8px 0;
    background: #fff;
    border: 1px solid #edeef1;
    border-radius: 2px;

    .searchBox {
      display: flex;

      .search-box {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-items: center;
        width: 17%;
        padding-bottom: 20px;
        margin-left: 3.8%;

        .search-label {
          display: inline-block;
          width: 88px;
          height: 100%;
          margin-right: 6px;
          font-size: 14px;
          line-height: 32px;
          color: #727272;
          text-align: right;
        }

        .search-inner {
          width: calc(100% - 88px);
          height: 30px;

          .checkbox {
            display: inline-block;
            height: 30px;
            line-height: 30px;
          }

          input {
            width: 100%;
          }
        }

        .screen-align-items {
          display: flex;
          align-items: center;
        }
      }

      .tui-w-t4 {
        .search-label {
          width: 66px;
        }

        .search-inner {
          width: calc(100% - 66px);
        }
      }

      .tui-w-t5 {
        .search-label {
          width: 76px;
        }

        .search-inner {
          width: calc(100% - 76px);
        }
      }

      .tui-w-t7 {
        .search-label {
          width: 96px;
        }

        .search-inner {
          width: calc(100% - 96px);
        }
      }

      .tui-w-t8 {
        .search-label {
          width: 106px;
        }

        .search-inner {
          width: calc(100% - 106px);
        }
      }

      .tui-w-t9 {
        .search-label {
          width: 116px;
        }

        .search-inner {
          width: calc(100% - 116px);
        }
      }

      .tui-w-t10 {
        .search-label {
          width: 126px;
        }

        .search-inner {
          width: calc(100% - 126px);
        }
      }

      :nth-child(1) {
        margin-left: 0;
      }
    }

    .search-operation {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 32px;
      margin-bottom: 20px;
      margin-left: 10px;
    }
  }
  // 表格数据
  .dataview-box {
    position: relative;
    padding: 0 10px 8px 0;
    margin-top: 8px;
    overflow: auto;
    background: #fff;
    border: solid 1px #e5e5e5;
    box-shadow: 0 3px 3px 0 rgb(223 223 223 / 18%);

    .view-btn {
      position: absolute;
      top: 10px;
      left: 8px;
      z-index: 15;
      width: 20px;
      height: 28px;
      font-size: 14px;
      line-height: 28px;
      color: #76b9d6;
      text-align: center;
      cursor: pointer;
      background-image: linear-gradient(to right, #dcf3fc, #f8fdff); /* 标准的语法（必须放在最后） */
      border: solid 1px #76b9d6;
      border-radius: 2px;
      transition: all 0.5s ease-in;

      &:hover {
        color: #fff;
        background-image: linear-gradient(to right, #7bc2e0, #b5deef);
      }
    }

    .view-btn + .tableview-bigbox .tableview-box {
      padding-left: 32px;
    }

    .view-btn + .tableview-bigbox .tableview-smallbox.tableview-box {
      padding-left: 8px;
    }

    .tableview-bigbox {
      position: relative;
      display: block;
      width: 100%;
      overflow: hidden;
      // 穿梭框
      .pickList-box {
        position: relative;
        display: inline-block;
        width: 240px;
        overflow: hidden;
        transition: all 0.3s ease-in;

        .pickList-box-title {
          height: 50px;
          padding: 10px 32px;
          margin-bottom: 8px;
          font-size: 14px;
          line-height: 30px;
          text-align: left;
          border-bottom: 1px solid #ebecef;
        }

        .pickList-box-title + t-picklist > div:first-child {
          width: calc(100% - 10px);
          margin-left: 10px;
        }

        .ml8 {
          margin-left: 8px;
        }
      }

      // 表格
      .tableview-box {
        display: block;
        float: right;
        width: 100%;
        padding-left: 8px;
        transition: all 0.3s ease-in;

        .table-operate {
          display: flex;
          justify-content: space-between;
          padding-top: 10px;
          margin-bottom: 10px;

          .table-operatebox {
            display: flex;
            align-items: center;

            .table-alarm {
              display: flex;
              justify-items: center;
              height: 30px;
              margin-left: 4px;
              line-height: 30px;
              border-left: 1px solid #e3e3e3;

              a.icon-refresh {
                display: flex;
                align-items: center;
                padding: 0 8px;
                font-size: 16px;
                color: #a5bcd3;
              }

              .level-span {
                width: 17px;
                height: 18px;
                margin-right: 6px;
              }

              span {
                display: flex;
                align-items: center;
                margin-left: 5px;
                font-size: 14px;
                color: #565656;
              }
            }
          }

          button,
          t-togglebutton {
            margin-right: 8px;
          }

          .table-toggle {
            display: flex;
            margin-right: 8px;

            button,
            t-fileupload,
            t-optionsbutton {
              margin-right: 0;
              margin-left: -1px;
            }

            button:first-child,
            .table-btn button:first-child {
              margin-left: 0;
            }
          }

          .table-operright {
            display: flex;
            justify-items: center;
            float: right;
          }
        }
        // 表格中的按钮
        .box-table {
          width: 100%;
          overflow: auto;

          .table-start {
            color: #4892d5;
            cursor: pointer;
          }

          .table-start:visited {
            color: #487ab9;
            cursor: pointer;
          }

          .table-start:hover {
            color: #4b93d5;
            cursor: pointer;
          }

          .table-ip {
            span {
              margin-right: 4px;
              vertical-align: middle;
            }

            span.table-span-tag {
              padding: 2px;
              color: #97bfc9;
              border: 1px solid #97bfc9;
              border-radius: 4px;
            }

            .table-span-img {
              height: 17px;
            }
          }

          .table-chartbox {
            display: flex;
            align-items: center;

            .table-chart {
              display: inline-block;
              width: 60px;
              height: 20px;
            }
          }

          .table-span-tag {
            display: inline-block;
          }

          .alarmSpan {
            height: 24px;
            padding: 0 4px;
            margin-right: 8px;
            line-height: 24px;
            border: 1px solid #aeaeae;
          }

          .tablea {
            position: relative;
            display: inline-block;
            margin-right: 8px;
            cursor: pointer;
            border-radius: 2px;

            span {
              display: block;
              width: 24px;
              height: 24px;
              font-size: 14px;
              line-height: 24px;
              color: #75b6d2;
              text-align: center;
              vertical-align: middle;
            }
          }

          .mr8 {
            margin-right: 8px;
          }

          .tabel-taskdiv {
            display: flex;
            align-items: center;

            span {
              min-width: 32px;
            }
          }
        }
        // 进度条 样式设置
        .info {
          position: relative;
          display: block;
          width: 56px;
          height: 23px;
          border-radius: 2px;

          span {
            display: inline-block;
            width: 56px;
            font-size: 13px;
            line-height: 22px;
            text-align: center;
          }

          t-progressbar {
            position: absolute;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 1;
            width: 100%;

            .ui-progressbar {
              background: none !important;
            }
          }
        }

        .info {
          background: #e3f9fc;
        }

        .exception {
          background: #fcebec;
        }

        .success {
          background: #d0efe6;
        }
      }

      .tableview-smallbox {
        display: block;
        width: calc(100% - 240px);
        padding-left: 16px;
        border-left: 1px solid #ebecef;
      }

      .table-btnmedia {
        display: none;
      }
    }
  }

  .ml8 {
    margin-left: 8px;
  }
}

//树模块
.treeView-box {
  position: relative;
  top: 0;
  bottom: 0;
  height: calc(100% - 50px);
  padding-top: 8px;

  .tree-tablebox {
    width: 100%;
    height: calc(100% + 7px);
    padding-left: 6px;
    margin-top: -7px;
    overflow: auto;
    background: #f3f4f7;

    .ui-scrolltop-sticky {
      position: fixed !important;
      bottom: 90px !important;
    }
  }
}

.tree-bigbox {
  width: 100%;
  min-width: 200px;
  height: 100%;
  overflow: auto;
  border: 1px solid #e3e3e3;

  .tree-titlebox {
    display: flex;
    justify-content: space-between;
    justify-items: center;
    height: 36px;
    padding: 0 10px;
    font-size: 14px;
    line-height: 36px;
    color: #333;
    border-bottom: 1px solid #e6e6e6;
    // 操作按钮
    .tree-op {
      display: flex;
      align-items: center;
      justify-content: space-between;

      a {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 100%;
        font-size: 10px;
        color: #959fbc;
        cursor: pointer;

        &:hover {
          font-size: 14px;
          color: #f19d0e;
        }
      }
    }
  }

  .tree-inner {
    margin: 0 10px;
  }
}

// 单表单
.h-listbox {
  min-width: 1160px;
  min-height: 100%;
  padding: 8px;
  background: #f3f4f7;

  .screen-listmore {
    position: absolute;
    top: 8px;
    right: 8px;
    bottom: 8px;
    left: 8px;
    min-width: 1160px;
    padding: 24px;
    overflow: hidden;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: 0 0 5px 0 rgb(0 0 0 / 6%);

    .form-button {
      margin-bottom: 24px;
    }

    .form-column-tab {
      height: 92%;
      padding: 0 74px;
      overflow: auto;

      .senior-form {
        position: relative;
        display: flex;
        align-items: center;
        min-width: 100px;
        margin-bottom: 5px;
        text-align: left;
        text-indent: 18px;
      }

      .form-required {
        position: absolute;
        top: 2px;
        left: -11px;
        font-size: 14px;
        color: #d02828;
      }

      .upbttn {
        margin-left: 10px;
        vertical-align: middle;
      }

      .form-button {
        button {
          width: 90px;
          margin-right: 10px;
        }
      }
    }

    .custom-form-title {
      padding: 4px 0 24px;
      font-size: 14px;
      color: @primary-color;
      text-indent: 19px;
    }

    span.icon-help {
      display: flex;
      width: 16px;
      height: 16px;
      padding-left: 5px;
      margin-top: 11px;
      font-size: 16px;
      color: #d0d3d7;
      cursor: pointer;

      &:hover {
        color: #a0a3a7;
      }
    }
  }

  .button-center {
    margin-top: 10px;
    text-align: center;
  }
}

//多表单
.more-listbox {
  min-width: 1160px;
  min-height: 100%;
  padding: 8px;
  background: #f3f4f7;

  .screen-listmore {
    position: absolute;
    top: 8px;
    right: 8px;
    bottom: 8px;
    left: 8px;
    min-width: 1160px;
    padding: 24px;
    overflow: hidden;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: 0 0 5px 0 rgb(0 0 0 / 6%);

    .form-button {
      margin-bottom: 24px;
    }

    span.required {
      position: absolute;
      top: 8px;
      left: -10px;
      width: 5px;
      height: 5px;
      font-family: SimSun;
      font-size: 10px;
      color: #d02828;
    }

    span.icon-help {
      width: 16px;
      height: 16px;
      margin-left: 8px;
      font-size: 16px;
      color: #d0d3d7;
      cursor: pointer;
    }

    span.form-tips {
      position: absolute;
      display: block;
      margin-top: 1px;
      margin-left: 5px;
      font-size: 12px;
      color: #ed4b32;
    }

    .form-box {
      height: 100%;
      padding: 0 74px 50px 0;
      overflow: auto;

      .senior-form {
        position: relative;
        display: inline-flex;
        align-items: center;
        width: calc(33.3% - 74px);
        margin-bottom: 20px;
        margin-left: 74px;

        .form-lable {
          width: 76px;
          font-size: 14px;
          color: #828282;
        }

        .form-input {
          width: calc(100% - 76px);

          input,
          textarea {
            width: 100%;
            min-width: 100px;
          }

          button.upload-btn {
            display: inline-flex;
            margin-left: 10px;
            vertical-align: bottom;
          }
        }
      }

      .custom-form-title {
        padding: 4px 0 24px;
        margin-left: 70px;
        font-size: 14px;
        color: @primary-color;
        text-indent: 16px;
      }

      .screen-button {
        display: inline-block;
        width: 100%;
        margin-top: 15px;
        text-align: center;

        button {
          width: 80px;
          margin-right: 10px;
        }
      }
    }

    .form-column-tab {
      height: 92%;
      padding: 0 74px 50px;
      overflow: auto;

      .senior-form {
        position: relative;
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        .form-lable {
          width: 76px;
          font-size: 14px;
          color: #828282;
        }

        .form-input {
          width: 705px;

          input,
          textarea {
            width: 100%;
            min-width: 100px;
          }

          t-checkbox,
          t-radiobutton {
            margin-right: 30px;
          }

          t-radiobutton {
            display: inline-block;
            margin-right: 10px;
          }

          button.upload-btn {
            display: inline-flex;
            margin-left: 10px;
            vertical-align: bottom;
          }
        }
      }

      .custom-form-title {
        padding: 4px 0 24px;
        font-size: 14px;
        color: @primary-color;
        text-indent: 19px;
      }

      .screen-button {
        display: inline-block;
        margin-left: 76px;
        text-align: left;

        button {
          width: 90px;
          margin-right: 10px;
        }
      }
    }
  }
}

// 弹窗
.form-column-tab {
  height: 92%;
  padding: 0 74px;
  overflow: auto;

  .senior-form {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .form-lable {
      width: 76px;
      font-size: 14px;
      color: #828282;
    }

    .form-input {
      width: 100%;

      input,
      textarea {
        width: 100%;
        min-width: 100px;
      }

      t-checkbox,
      t-radiobutton {
        margin-right: 30px;
      }

      button.upload-btn {
        display: inline-flex;
        margin-left: 10px;
        vertical-align: bottom;
      }
    }
  }

  .form-required {
    position: absolute;
    left: -11px;
    font-size: 14px;
    color: #d02828;
  }

  .upbttn {
    margin-left: 10px;
    vertical-align: middle;
  }

  .form-button {
    button {
      width: 90px;
      margin-right: 10px;
    }
  }
}

.splitter-box {
  border: none !important;

  .ui-splitter-gutter {
    width: 4px !important;
    background: #f3f4f7 !important;
    border-right: none !important;
  }
}

.container-box .searchBox-box .searchBox .search-box .search-inner {
  .ui-calendar-w-btn {
    width: 100% !important;

    input {
      width: calc(100% - 30px) !important;
    }
  }
}

// 自适浏览器
@media only screen and (max-width: 1366px) {
  .handle-box {
    min-width: 100% !important;
  }

  .treeView-box {
    overflow: auto;
  }

  //总量数据统计指标部分
  .amount-chartbox {
    .amount-chart-lf {
      .amount-img {
        width: 42px;
        height: 42px;

        .amount-frontIcon {
          font-size: 40px;
        }

        .amount-afterIcon {
          left: 18px;
          width: 20px;
          height: 20px;
          font-size: 13px;
          line-height: 20px;
        }
      }

      .amount-chart-name {
        padding-left: 10px;

        span {
          display: block;
        }

        .number {
          font-size: 22px;
          font-weight: bold;
          color: #565656;
        }

        .name {
          font-size: 12px;
          color: #565656;
        }
      }
    }

    .amount-chart-rg {
      .amount-charts {
        width: 77px;
        height: 22px;
      }

      .amount-trend-number {
        font-size: 5px;
        color: #22c87c;
      }
    }
  }

  //对比数据统计指标部分
  .amount-char {
    .amount-inner {
      .amount-list {
        width: 30%;
      }

      .amount-bar {
        width: 41%;
      }
    }
  }

  // 查询模块
  .container-box .dataview-box .tableview-bigbox {
    .table-btnmedia {
      display: block;
    }

    .table-btn {
      display: none;
    }
  }

  .container-box {
    .searchBox-box {
      .searchBox {
        .search-box {
          display: flex;
          flex-shrink: 0;
          align-items: center;
          justify-items: center;
          width: 18%;
          padding-bottom: 20px;
          margin-left: 30px;
        }

        :nth-child(1) {
          margin-left: 0;
        }
      }
    }
  }

  // 概览页样式
  .tui-expOver {
    .tui-expOver-bottom {
      min-height: 270px;
    }

    .tui-expOver-table {
      height: calc(100% - 40px);
      overflow: hidden;
    }

    .tui-threat-centerbox .tui-expOver-listone ul li > div .tui-home-alarm span:last-child {
      margin-left: 0;
    }
  }
}

@media only screen and (min-width: 1366px) and(max-width: 1570px) {
  //总量数据统计指标部分
  .amount-chartbox {
    .amount-chart-lf {
      .amount-img {
        width: 42px;
        height: 42px;

        .amount-frontIcon {
          font-size: 40px;
        }

        .amount-afterIcon {
          left: 18px;
          width: 20px;
          height: 20px;
          font-size: 13px;
          line-height: 20px;
        }
      }

      .amount-chart-name {
        padding-left: 10px;

        span {
          display: block;
        }

        .number {
          font-size: 22px;
          font-weight: bold;
          color: #565656;
        }

        .name {
          font-size: 12px;
          color: #565656;
        }
      }
    }

    .amount-chart-rg {
      .amount-charts {
        width: 77px;
        height: 22px;
      }

      .amount-trend-number {
        font-size: 5px;
        color: #22c87c;
      }
    }
  }

  //对比数据统计指标部分
  .amount-char {
    .amount-inner {
      .amount-list {
        width: 30%;
      }

      .amount-bar {
        width: 41%;
      }
    }
  }

  // 查询模块
  .container-box .dataview-box .tableview-bigbox {
    .table-btnmedia {
      display: block;
    }

    .table-btn {
      display: none;
    }
  }

  .container-box {
    .searchBox-box {
      .searchBox {
        .search-box {
          display: flex;
          flex-shrink: 0;
          align-items: center;
          justify-items: center;
          width: 18%;
          padding-bottom: 20px;
          margin-left: 22px;
        }

        :nth-child(1) {
          margin-left: 0;
        }
      }
    }
  }

  // 概览页样式
  .tui-expOver {
    .tui-expOver-bottom {
      min-height: 270px;
    }

    .tui-expOver-table {
      height: calc(100% - 40px);
      overflow: hidden;
    }

    .tui-threat-centerbox .tui-expOver-listone ul li > div .tui-home-alarm span:last-child {
      margin-left: 0;
    }
  }

  // tree布局
}

// 2023-2-21解决 TreeDropdown 组件 宽度没有对齐的方式ui-tree-bigbox
.container-box .searchBox-box .searchBox .search-inner {
  .ui-tree-bigbox,
  .ui-dropdown {
    min-width: 100% !important;
  }
}

// 2023-3-7 修改布局模式

// 查询布局结构
.query-box {
  position: relative;
  min-height: 77px;
  max-height: 77px;
  overflow: hidden;
  transition: max-height 0.5s;

  .query-searchBox {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    float: left;
    width: calc(100% - 120px);

    .search-box:nth-child(1),
    .search-box:nth-child(4n + 1) {
      margin-left: 0;
    }

    .search-box {
      display: flex;
      align-items: center;
      justify-items: center;
      width: 22.5%;
      padding-bottom: 20px;
      margin-left: 22px;
    }

    .search-label {
      display: inline-block;
      width: 88px;
      height: 100%;
      margin-right: 6px;
      font-size: 14px;
      line-height: 32px;
      color: #727272;
      text-align: right;
    }

    .search-inner {
      input {
        width: 100%;
      }

      width: calc(100% - 88px);
      height: 30px;
    }
  }

  .query-operation {
  }

  .query-btn {
    position: absolute;
    right: 0;
    bottom: -4px;
    width: 48px;
    height: 20px;
    font-size: 16px;
    line-height: 20px;
    color: #ccc;
    text-align: center;
    cursor: pointer;
    background: url("./assets/imgs/i-bg.png");

    &:hover {
      color: #808080;
    }
  }
}

.query-senior {
  max-height: 500px;
  transition: max-height 0.5s;
}

.container-box .query-box .query-searchBox .search-inner {
  .ui-dropdown {
    min-width: 100% !important;
  }
}

// 滚动条样式
.kita-box {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #f3f4f7;

  .ui-scrolltop-sticky {
    position: fixed !important;
    bottom: 90px !important;
  }
}

.container-box .query-box .query-searchBox .search-box .search-inner {
  .ui-calendar-w-btn {
    width: 100% !important;

    input {
      width: calc(100% - 30px) !important;
    }
  }
}

// 表格固定滚动条 需要在 regular-box添加
.regular-box {
  height: 100%;
  overflow: auto;
}

.handle-bar ~ .regular-box {
  height: calc(100% - 50px);
  overflow: auto;
}

.ui-scrolltop {
  position: fixed;
  right: 20px;
  bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  cursor: pointer;
  border: 1px solid rgb(0 0 0 / 10%);
  border-radius: 50%;
  transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
}

.ui-scrolltop-sticky {
  position: sticky;
}

.ui-scrolltop-sticky.ui-link {
  margin-left: auto;
}

.ui-scrolltop .ui-scrolltop-icon {
  font-size: 18px;
  color: #fff;
}

.ui-scrolltop {
  background: @primary-active-color;
  box-shadow: 0 2px 2px -1px @box-shadow-color1, 0 -1px 2px 0 @box-shadow-color1, 0 1px 6px 0 @box-shadow-color1;
}

.ui-scrolltop.ui-link:hover {
  background: @primary-hover-color;
}

.ui-scrolltop .ui-scrolltop-icon {
  color: @text-color-inverse;
}

// 2023/3/15 解决form布局
.t-form-item-label > label::after {
  content: "" !important;
}

.form-remarks {
  position: relative;
  top: -40px;
}

.form-column-tab {
  .t-col-17 {
    width: calc(70.83333333% - 100px) !important;
  }
}

.form-column-tab {
  .t-form-item-control {
    .t-form-item-children {
      t-dropdown {
        .ui-dropdown {
          min-width: auto !important;
        }
      }
    }
  }
}
