<div class="multi-select-container"> <ng-content></ng-content> </div>
<ng-template #overlayTmpl>
  <div class="ui-dropdown-panel" #target>
    <div class="ui-dropdown-filter-container">
      <input type="text" autocomplete="off" class="ui-dropdown-filter ui-inputtext ui-widget ui-state-default ui-corner-all" (input)="search(searchInput.value)" #searchInput />
      <span class="ui-dropdown-filter-icon icon-search2"></span>
    </div>
    <div class="ui-dropdown-items-wrapper ui-dropdown-databox" style="position: relative; max-height: 200px">
      <ul role="listbox" class="ui-dropdown-items ui-dropdown-list ui-widget-content ui-widget ui-corner-all ui-helper-reset ui-dropdown-list-databox">
        <div class="ui-numberMoreLayout"></div>
        <ng-container *ngFor="let child of fields; let i = index">
          <ng-container *ngIf="!child.hidden">
            <li
              class="ui-dropdown-item-group"
              [tTooltip]="child.label"
              tooltipPosition="bottom"
              tooltipZIndex="10001"
              [ngClass]="{ 'ui-state-highlight': tabindex == i }"
              (click)="onTabClick(child, i)"
              style="width: 120px; position: relative; z-index: 1"
            >
              <div>
                <span class="ui-dr-spanlable">{{ child.label }}</span>
                <i class="i-latebox icon-paginato-right"></i>
              </div>
            </li>
            <ul class="ui-dropdown-item-group-child-close ui-numberMoreLayout-ul" [class.ui-dropdown-item-group-child-open]="tabindex == i">
              <ng-container *ngFor="let item of child.items">
                <li *ngIf="!item.hidden" role="option" class="ui-numberMoreLayout ui-dropdown-item ui-corner-all">
                  <t-checkbox [tTooltip]="item.label" [disabled]="item.isDsabled" tooltipPosition="bottom" tooltipZIndex="10001" [label]="item.label" [value]="item.value" [(ngModel)]="selectedIds" (onChange)="onChange(item, $event)"></t-checkbox>
                </li>
              </ng-container>
            </ul>
          </ng-container>
        </ng-container>
      </ul>
    </div>
  </div>
</ng-template>
