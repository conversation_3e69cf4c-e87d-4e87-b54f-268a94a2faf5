import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { OverlayContainer, OverlayModule } from '@angular/cdk/overlay';

import { CheckboxModule, TooltipModule } from '@tui/component-library';

import { FieldMutiSelectComponent } from './field-muti-select.component';
import { MultiSelectorAnchorDirective } from './multi-selector-anchor.directive';
import { CustomOverlayContainer } from 'src/app/services/overlay-container.service';

@NgModule({
  declarations: [FieldMutiSelectComponent, MultiSelectorAnchorDirective],
  imports: [CommonModule, FormsModule, ReactiveFormsModule, CheckboxModule, TooltipModule, OverlayModule],
  providers: [
    {
      provide: OverlayContainer,
      useClass: CustomOverlayContainer,
    },
  ],
  exports: [FieldMutiSelectComponent, MultiSelectorAnchorDirective],
})
export class FieldMutiSelectModule {}
