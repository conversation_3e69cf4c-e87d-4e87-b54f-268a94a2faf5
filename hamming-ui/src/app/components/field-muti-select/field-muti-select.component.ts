import { Component, ElementRef, EventEmitter, Input, NgZone, OnInit, Output, ViewChild, ViewContainerRef } from '@angular/core';

import { Overlay } from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';
import { BehaviorSubject, Subscription, fromEvent, merge } from 'rxjs';
import { debounceTime, filter, first, take } from 'rxjs/operators';

@Component({
  selector: 'field-muti-select',
  templateUrl: './field-muti-select.component.html',
  styleUrls: ['./field-muti-select.component.less'],
})
export class FieldMutiSelectComponent implements OnInit {
  @ViewChild('overlayTmpl') overlayTmpl: any;
  @ViewChild('target') target: ElementRef;

  // 选择字段
  @Input() fields = [];

  // 已选字段
  @Input() set selectedIds(val: string[]) {
    this._selectedIds = [...val];
  }

  get selectedIds() {
    return this._selectedIds;
  }

  @Output() valueChange = new EventEmitter();

  anchor: ElementRef;

  tabindex = 0;

  subscription: Subscription = Subscription.EMPTY;

  private overlayRef: any;

  private _selectedIds = [];
  private search$ = new BehaviorSubject('');

  constructor(private ngZone: NgZone, private viewRef: ViewContainerRef, private overlay: Overlay) {}

  ngOnInit(): void {
    this.search$.pipe(debounceTime(300)).subscribe((txt) => {
      for (const field of this.fields) {
        let len = 0;
        field.items.map((el) => {
          if (~el.label.indexOf(txt)) {
            el.hidden = false;
          } else {
            el.hidden = true;
            len++;
          }
        });

        if (field.items.length == len) {
          field.hidden = true;
        } else {
          field.hidden = false;
        }
      }
    });
  }

  /**
   * 搜索
   * @param txt 文本
   */
  search(txt: string) {
    this.search$.next(txt);
  }

  showOverlay() {
    if (!this.overlayRef) {
      this.overlayRef = this.overlay.create({
        hasBackdrop: false,
        disposeOnNavigation: true,
        positionStrategy: this.getDropdownStrategy(),
      });
    }

    if (this.overlayRef && !this.overlayRef.hasAttached()) {
      const portal = new TemplatePortal(this.overlayTmpl, this.viewRef);
      this.overlayRef.attach(portal);

      this.ngZone.onStable.pipe(take(1)).subscribe(() => this.overlayRef.updatePosition());

      this.subscription = new Subscription();

      // // // 添加body mousemove移动监听
      this.subscription.add(
        this.ngZone.runOutsideAngular(() => {
          merge(
            fromEvent(document, 'click').pipe(filter((event) => !this.anchor.nativeElement.contains(event.target) && !this.target?.nativeElement.contains(event.target))),
            fromEvent(window, 'scroll', { capture: true }).pipe(
              filter((event) => !this.anchor.nativeElement.contains(event.target) && !this.target?.nativeElement.contains(event.target)),
              first(),
            ),
          ).subscribe(() => {
            this.ngZone.run(() => this.dismissOverlay());
          });
        }),
      );
    }
  }

  dismissOverlay() {
    this.overlayRef.detach();
    this.subscription.unsubscribe();
  }

  onTabClick(item: any, index: number) {
    this.tabindex = index;
  }

  onChange(item: any, checked: boolean) {
    this.valueChange.emit({
      item: item,
      checked: checked,
    });
  }

  getDropdownStrategy() {
    const strategy = this.overlay.position().flexibleConnectedTo(this.anchor.nativeElement).withFlexibleDimensions(false).withViewportMargin(8).withLockedPosition();
    return this.setConnectedPositions(strategy);
  }

  setConnectedPositions(strategy) {
    const primaryX = 'start';
    const secondaryX = primaryX === 'start' ? 'end' : 'start';
    const primaryY = 'top';
    const secondaryY = primaryY === 'top' ? 'bottom' : 'top';
    return strategy.withPositions([
      {
        originX: primaryX,
        originY: secondaryY,
        overlayX: primaryX,
        overlayY: primaryY,
      },
      {
        originX: primaryX,
        originY: primaryY,
        overlayX: primaryX,
        overlayY: secondaryY,
      },
      {
        originX: secondaryX,
        originY: secondaryY,
        overlayX: secondaryX,
        overlayY: primaryY,
      },
      {
        originX: secondaryX,
        originY: primaryY,
        overlayX: secondaryX,
        overlayY: secondaryY,
      },
    ]);
  }
}
