import { Directive, ElementRef, HostListener } from '@angular/core';
import { FieldMutiSelectComponent } from './field-muti-select.component';

@Directive({
  selector: '[multiSelectorAnchor]',
})
export class MultiSelectorAnchorDirective {
  constructor(private element: ElementRef, private fieldMutiSelectComponent: FieldMutiSelectComponent) {
    this.fieldMutiSelectComponent.anchor = element;
  }

  @HostListener('click')
  onClick() {
    this.fieldMutiSelectComponent.showOverlay();
  }
}
