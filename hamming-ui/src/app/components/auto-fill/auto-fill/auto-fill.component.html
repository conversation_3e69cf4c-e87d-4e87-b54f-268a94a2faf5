<ng-content></ng-content>
<ng-template #suggestTpl>
  <ul class="auto-fill-dropdown">
    <li #items class="auto-fill-dropdown-item" *ngFor="let suggestion of filteredSuggestions; let i = index" [class.focus]="i === activeIndex" (click)="selectSuggestion(suggestion)">
      <ng-container *ngIf="suggestionTemplate; else defaultSuggestion">
        <ng-container *ngTemplateOutlet="suggestionTemplate; context: { $implicit: suggestion }"></ng-container>
      </ng-container>
      <ng-template #defaultSuggestion>{{ suggestion.label }}</ng-template>
    </li>

    <li class="auto-fill-dropdown-item notfound" *ngIf="filteredSuggestions.length === 0">
      <span *ngIf="loading"> </span>
      <span *ngIf="!loading">{{ notFoundContent }}</span>
    </li>
  </ul>
</ng-template>
