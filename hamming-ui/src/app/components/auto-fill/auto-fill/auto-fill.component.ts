import {
  AfterContentInit,
  ChangeDetectorRef,
  Component,
  ContentChild,
  ElementRef,
  EmbeddedViewRef,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  QueryList,
  TemplateRef,
  ViewChild,
  ViewChildren,
  ViewContainerRef,
} from '@angular/core';

import { Subject, Subscription } from 'rxjs';

import { AutoFillTriggerDirective } from '../auto-fill-trigger.directive';
import { Suggestion, ENTER, LEFT_ARROW, RIGHT_ARROW, TAB, ESCAPE, UP_ARROW, DOWN_ARROW } from '../model';
import { PopupService } from '../popup.service';
import { getCaretCoordinates } from '../textarea-caret-position';
import { FormatSuggestion } from '../util';

export interface AutoFillSearchTypes {
  text: string;
  prefix: string;
  snippets: Snippet[];
}
export interface Snippet {
  prefix: string;
  startColumn: number;
  endColumn: number;
  text: string;
}

@Component({
  selector: 'app-auto-fill',
  templateUrl: './auto-fill.component.html',
  styleUrls: ['./auto-fill.component.less'],
  providers: [PopupService],
})
export class AutoFillComponent implements OnInit, AfterContentInit, OnChanges, OnDestroy {
  @ContentChild(AutoFillTriggerDirective, { static: true }) trigger!: AutoFillTriggerDirective;
  @ViewChild('suggestTpl') suggestionContainer: TemplateRef<any>;
  @ViewChildren('items') items?: QueryList<ElementRef>;

  @Input() suggestions: Suggestion[] = [];
  @Input() suggestionTemplate: TemplateRef<any>;
  /**
   * 词缀 支持前缀/后缀模式, 字符串形式默认指定前缀,后缀为空格" "
   */
  @Input() prefix: string[] = ['@'];
  @Input() loading = false;
  @Input() placement = 'bottom';

  @Output() searchChange: EventEmitter<AutoFillSearchTypes> = new EventEmitter();

  destroy$: Subject<boolean> = new Subject<boolean>();

  get cursorIndex() {
    return this.triggerNativeElement?.selectionStart || 0;
  }

  get value() {
    return this.triggerNativeElement?.value.replace(/[\r\n]/g, ' ') || '';
  }

  filteredSuggestions = [];
  // 上次数据
  previousValue: string;

  snippets: any[];

  activeIndex = -1;

  isOpen = false;
  notFoundContent = '无匹配结果, ESC键或者空格结束匹配';
  overlayRef: any;

  portal: EmbeddedViewRef<any>;

  overlayOutsideClickSubscription: Subscription;

  get triggerNativeElement() {
    return this.trigger.el.nativeElement;
  }
  get focusItemElement() {
    // @ts-ignore
    const itemArr = this.items.toArray();
    if (itemArr && itemArr[this.activeIndex]) {
      return itemArr[this.activeIndex].nativeElement;
    }
    return null;
  }

  constructor(private cdr: ChangeDetectorRef, private viewContainerRef: ViewContainerRef, private popupService: PopupService) {}

  ngOnInit() {}

  ngAfterContentInit() {
    if (this.trigger) {
      this.bindTriggerEvents();
      this.closeDropdown();
      this.overlayRef = null;
    }
  }

  ngOnChanges(changes) {
    if (Object.prototype.hasOwnProperty.call(changes, 'suggestions') && this.isOpen) {
      this.previousValue = null;
      this.resetDropdown(false);
    }
  }

  ngOnDestroy() {
    if (this.overlayRef) {
      this.overlayRef.remove();
    }

    this.closeDropdown();
  }

  closeDropdown() {
    if (this.overlayRef && this.overlayRef.hasAttached()) {
      this.overlayRef.detach();
      this.overlayOutsideClickSubscription.unsubscribe();
      this.isOpen = false;
      this.cdr.markForCheck();
    }
  }
  openDropdown() {
    this.attachOverlay();
    this.isOpen = true;
    this.cdr.markForCheck();
  }

  handleInput(event) {
    const startIndex = this.triggerNativeElement.selectionStart;
    this.resetDropdown(startIndex);
  }
  handleKeydown(event) {
    const keyCode = event.keyCode;
    if (this.isOpen && keyCode === ENTER && this.activeIndex !== -1 && this.filteredSuggestions.length) {
      // 映射选中数据
      this.selectSuggestion(this.filteredSuggestions[this.activeIndex]);
      event.preventDefault();
    } else if (keyCode === LEFT_ARROW || keyCode === RIGHT_ARROW) {
      // 重置下拉
      this.resetDropdown();
      event.stopPropagation();
    } else {
      if (this.isOpen && (keyCode === TAB || keyCode === ESCAPE)) {
        // 关闭下拉
        this.closeDropdown();
        return;
      }
      if (this.isOpen && keyCode === UP_ARROW) {
        // 向上选择
        this.setItemActive(true);
        event.preventDefault();
        event.stopPropagation();
      }
      if (this.isOpen && keyCode === DOWN_ARROW) {
        // 向下选择
        this.setItemActive(false);
        event.preventDefault();
        event.stopPropagation();
      }
    }
  }

  setItemActive(prev: boolean) {
    const activeIndex = this.activeIndex + (prev ? -1 : 1);

    this.activeIndex = prev ? Math.max(activeIndex, 0) : Math.min(activeIndex, this.filteredSuggestions.length - 1);

    this.scrollToFocusItem();
  }

  scrollToFocusItem() {
    if (this.focusItemElement) {
      this.focusItemElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest',
      });
    }
  }

  handleClick() {
    this.resetDropdown();
  }

  selectSuggestion(suggestion) {
    const { prefix, startColumn, endColumn } = this.snippets[0];

    const value = FormatSuggestion(prefix, suggestion);

    this.trigger.insertText({
      text: value,
      startColumn,
      endColumn,
    });
    this.closeDropdown();
    this.activeIndex = -1;
  }

  subscribeOverlayOutsideClick() {
    const canCloseDropdown = (event) => {
      const clickTarget = event.target;
      return this.isOpen && clickTarget !== this.trigger.el.nativeElement && !this.overlayRef.overlayElement.contains(clickTarget);
    };
    const subscription = new Subscription();
    subscription.add(this.overlayRef.outsidePointerEvents.subscribe((event) => canCloseDropdown(event) && this.closeDropdown()));
    return subscription;
  }

  bindTriggerEvents() {
    this.trigger.onInput.subscribe((e) => this.handleInput(e));
    this.trigger.onKeydown.subscribe((e) => this.handleKeydown(e));
    this.trigger.onClick.subscribe(() => this.handleClick());
  }

  suggestionsFilter(prefix: string, text: string, emit: boolean) {
    if (this.previousValue === text && text !== prefix) {
      return;
    }
    this.previousValue = text;

    if (emit) {
      this.searchChange.emit({
        text: text,
        prefix: prefix,
        snippets: this.snippets,
      });
    }

    const searchValue = text.toLowerCase();
    this.filteredSuggestions = this.suggestions.filter((suggestion) => suggestion.value.toLowerCase().startsWith(searchValue));
  }

  /**
   * 重置下拉数据
   * @returns
   */
  resetDropdown(emit = true) {
    this.snippets = this.handleTextChange(this.value, this.cursorIndex);

    if (!this.snippets.length || !this.canOpen()) {
      this.closeDropdown();
      return;
    }

    const { prefix, text } = this.snippets[0];
    this.suggestionsFilter(prefix, text, emit);
    const activeIndex = this.filteredSuggestions.findIndex((el) => el.value == text);
    this.activeIndex = activeIndex >= 0 ? activeIndex : 0;
    this.openDropdown();
  }

  canOpen() {
    const element = this.triggerNativeElement;
    return !element.readOnly && !element.disabled;
  }

  handleTextChange(text: string, cursorIndex: number) {
    const snippets = [];

    let i = this.prefix.length;

    while (i > 0) {
      const prefix = this.prefix[i - 1];
      const startColumn = text.lastIndexOf(prefix, cursorIndex);

      if (startColumn >= 0) {
        // 匹配右侧结束符位置，默认空格作为结束符
        const endColumn = text.indexOf(' ', cursorIndex) >= 0 ? text.indexOf(' ', cursorIndex) : text.length;
        snippets.push({
          prefix: prefix,
          startColumn,
          endColumn,
          text: text.substring(prefix.length),
        });
      }
      i--;
    }

    return snippets
      .sort((a, b) => b.startColumn - a.startColumn)
      .map((item) => ({
        text: text.substring(item.startColumn, item.endColumn),
        ...item,
      }));
  }

  reposition() {
    const { startColumn } = this.snippets[this.snippets.length - 1];
    const coordinates = getCaretCoordinates(this.triggerNativeElement, startColumn);
    let top = coordinates.top - this.triggerNativeElement.scrollTop;
    const left = coordinates.left - this.triggerNativeElement.scrollLeft;
    // this.positionStrategy.withDefaultOffsetX(left).withDefaultOffsetY(top);
    if (this.placement === 'bottom') {
      top += coordinates.height + 2;
    }
    if (this.placement === 'top') {
      top -= coordinates.height - 2;
      // this.positionStrategy.withPositions([...DEFAULT_MENTION_TOP_POSITIONS]);
    }

    this.overlayRef.positionChange({
      left,
      top,
    });
  }

  /**
   * 打开overlay
   */
  attachOverlay() {
    if (!this.overlayRef) {
      this.overlayRef = this.popupService.create(this.triggerNativeElement);
    }

    if (this.overlayRef && !this.overlayRef.hasAttached()) {
      this.portal = this.viewContainerRef.createEmbeddedView(this.suggestionContainer);
      this.overlayRef.attach(this.portal);
      this.overlayRef.onScroll.subscribe(this.onScroll.bind(this));
      this.overlayRef.onResize.subscribe(this.reposition.bind(this));
      this.overlayOutsideClickSubscription = this.subscribeOverlayOutsideClick();
    }
    this.reposition();
  }

  onScroll(isInViewPort) {
    console.log('触发滚动');
    if (isInViewPort) {
      this.closeDropdown();
    }
  }
}
