import { Directive, Input } from '@angular/core';

import { AutoFillComponent } from './auto-fill/auto-fill.component';

@Directive({
  selector: '[autoFillSnippet]',
})
export class AutoFillSnippetDirective {
  @Input() prefix: string[] = ['${:}'];

  @Input() triggerChars: string[] = [',', '('];
  @Input() retriggerChars: string[] = [')', '}'];

  lookupData: any[];
  visibleDatas: Set<any> = new Set();

  constructor(private autoFill: AutoFillComponent) {
    this.autoFill.handleTextChange = this.handleTextChange.bind(this);
  }

  handleTextChange(text: string, cursorIndex: number) {
    const snippets = [];
    const [prefix, suffix] = this.prefix[0].split(':');
    const triggerChars = new Set([...this.triggerChars, ...this.retriggerChars]);

    const startColumn = text.lastIndexOf(prefix, cursorIndex);

    // 匹配右侧结束符位置，默认空格作为结束符
    let endColumn = text.indexOf(' ', cursorIndex) >= 0 ? text.indexOf(' ', cursorIndex) : text.length;

    if (suffix) {
      const suffixPos = text.indexOf(suffix, cursorIndex);
      if (suffixPos >= 0) {
        endColumn = suffixPos + 1;
      }
    }

    if (startColumn >= 0) {
      for (const suffix of this.retriggerChars) {
        const isValid = this.validateHintText(text, suffix, cursorIndex - 1 || 0, startColumn);
        if (!isValid) {
          return [];
        }
      }

      // 匹配到前缀和参数
      let left = startColumn + prefix.length;
      // 插入新的开始位置
      snippets.push({
        prefix: this.prefix[0],
        startColumn: startColumn,
        endColumn: endColumn,
        text: '',
      });

      let words = '';
      while (left <= endColumn) {
        const charCode = text.charAt(left);
        if (!charCode || triggerChars.has(charCode)) {
          if (snippets.length) {
            snippets[0].endColumn = left;
            snippets[0].text = words;
          }

          if (left >= cursorIndex) {
            break;
          }

          // 插入新的开始位置
          snippets.unshift({
            prefix: '',
            endColumn: left + 1,
            startColumn: left + 1,
            text: '',
          });

          words = '';
          left++;
          continue;
        }
        words += charCode;
        left++;
      }

      // 修正结束坐标
      snippets[snippets.length - 1].endColumn = endColumn;
    }

    return snippets;
  }

  /**
   * 验证是否可以进行提示
   * 左侧字符是)、}这种不进行提示
   * @param text 文本
   * @param suffix 后缀
   * @param cursorIndex 光标位置
   * @param cutIndex 裁切位置
   * @returns
   */
  validateHintText(text: string, suffix: string, cursorIndex: number, cutIndex) {
    return text.lastIndexOf(suffix, cursorIndex) < cutIndex;
  }
}
