import { StrFormat } from 'src/app/utils';
import { Suggestion } from './model';

export const regEscape = (txt: string) => txt.replace(/([\$\?\[\]\(\)\{\}])/g, '\\$1').trim();

/**
 * @hidden
 */
export const TransformerPrefix = (prefix: string[] | string): string[] => {
  if (Array.isArray(prefix)) {
    return prefix;
  } else {
    return [prefix];
  }
};

/**
 * @hidden
 */
export const ScrollableParents = (element) => {
  const parentElements = [];
  let parent = element.parentElement;
  while (parent) {
    const style = GetStyle(parent);

    if (/auto|scroll/.test(`${style.overflow}${style.overflowX}${style.overflowY}`)) {
      parentElements.push(parent);
    }
    parent = parent.parentElement;
  }
  parentElements.push(window);
  return parentElements;
};

export const GetStyle = (element) => {
  return window.getComputedStyle ? window.getComputedStyle(element) : element.currentStyle;
};

export const FormatSuggestion = (pattern: string, option: Suggestion) => {
  const [prefix, suffix] = pattern.split(':');
  return StrFormat(`${prefix}{0}${suffix || ''}`, option.format || option.value);
};

const rectOfHiddenElement = (element) => {
  const { display, left, position } = element.style;

  element.style.display = '';
  element.style.left = '-10000px';
  element.style.position = 'absolute';

  const rect = element.getBoundingClientRect();

  element.style.display = display;
  element.style.left = left;
  element.style.position = position;

  return rect;
};

export const Offset = (element) => {
  let rect = element.getBoundingClientRect();
  const { left, top, right, bottom } = rect;

  if (!rect.height && !rect.width) {
    rect = rectOfHiddenElement(element);
  }

  return {
    top: top,
    left: left,
    right: right,
    bottom: bottom,
    height: rect.height,
    width: rect.width,
  };
};
