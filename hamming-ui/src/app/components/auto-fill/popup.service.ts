import { Injectable, NgZone, EmbeddedViewRef, EventEmitter } from '@angular/core';

import { Subscription, fromEvent, merge } from 'rxjs';
import { auditTime } from 'rxjs/operators';

import { Offset, ScrollableParents } from './util';

@Injectable()
export class PopupService {
  host: HTMLElement;
  overlay: HTMLElement;
  anchor: HTMLElement;

  attachedPortal: any;
  clickListener: (_: Event) => void;
  subscription: Subscription;

  outsidePointerEvents: EventEmitter<any> = new EventEmitter();
  resizeEvents: EventEmitter<any> = new EventEmitter();
  scrollEvents: EventEmitter<any> = new EventEmitter();

  constructor(private ngZone: NgZone) {
    this.host = this.createHostElement();
    this.clickListener = (event: Event) => {
      const target = event.target;
      if (this.hasAttached()) {
        // @ts-ignore
        if (!this.overlay.contains(target)) {
          this.ngZone.run(() => {
            this.outsidePointerEvents.next(event);
          });
        }
      }
    };
  }

  hasAttached() {
    return !!this.attachedPortal;
  }

  createPaneElement(host) {
    const pane = document.createElement('div');
    pane.classList.add('cdk-overlay-pane');
    pane.style.left = '-10000px';
    pane.style.top = '0px';
    host.appendChild(pane);
    return pane;
  }

  createHostElement() {
    let host = document.getElementById('overlay-container');
    if (!host) {
      host = document.createElement('div');
      host.id = 'overlay-container';
      host.classList.add('overlay-container');
      host.classList.add('hamming');
      document.body.appendChild(host);
    }

    return host;
  }

  create(anchor: HTMLElement) {
    this.overlay = this.createPaneElement(this.host);
    this.anchor = anchor;
    return {
      remove: () => {
        this.detach();

        // 销毁div
        if (this.overlay && this.overlay.parentNode) {
          this.overlay.parentNode.removeChild(this.overlay);
        }

        this.overlay = null;
      },
      detach: () => {
        this.detach();
      },
      hasAttached: this.hasAttached.bind(this),
      attach: (viewRef: EmbeddedViewRef<any>) => {
        this.attachedPortal = viewRef;
        viewRef.rootNodes.forEach((rootNode) => this.overlay.appendChild(rootNode));
        viewRef.detectChanges();
        this.subscription = new Subscription();
        this.bindScrollable(this.anchor);
        this.bindResize();
        this.ngZone.runOutsideAngular(() => this.addEventListeners(document.body));
      },
      overlayElement: this.overlay,
      outsidePointerEvents: this.outsidePointerEvents,
      onResize: this.resizeEvents,
      onScroll: this.scrollEvents,
      positionChange: this.reposition.bind(this, this.overlay),
    };
  }

  reposition(container: HTMLElement, offset) {
    const coordinate = this.position(offset);
    container.style.left = coordinate.left + 'px';
    container.style.top = coordinate.top + 'px';
  }

  position(addition) {
    // 获取element偏移量
    const elementOffset = Offset(this.anchor);

    return {
      left: elementOffset.left + addition.left,
      top: elementOffset.top + addition.top,
      height: elementOffset.height,
      width: elementOffset.width,
    };
  }

  bindScrollable(nativeElement: HTMLElement) {
    const parents = ScrollableParents(nativeElement);
    this.ngZone.runOutsideAngular(() => {
      const observables = parents.map((p) => fromEvent(p, 'scroll').pipe(auditTime(1000 / 60)));
      const subscriber = (e) => {
        const target = e.target;
        const isParent = parents.filter((p) => p === target).length > 0;
        const isDocument = target === document;
        const isWindow = target === window;
        if (isParent || isDocument || isWindow) {
          this.ngZone.run(() => this.scrollEvents.next(this.isVisible(nativeElement, target)));
        }
      };

      this.subscription.add(merge(...observables).subscribe(subscriber));
    });
  }

  bindResize() {
    this.ngZone.runOutsideAngular(() => {
      const observable = fromEvent(window, 'resize').pipe(auditTime(1000 / 60));

      this.subscription.add(
        observable.subscribe(() => {
          this.resizeEvents.next();
        }),
      );
    });
  }

  detach() {
    this.removeEventListeners();
    this.subscription.unsubscribe();
    if (this.attachedPortal) {
      this.attachedPortal.destroy();
      this.attachedPortal = null;
    }
  }

  removeEventListeners() {
    const body = document.body;
    body.removeEventListener('click', this.clickListener, true);
    body.removeEventListener('contextmenu', this.clickListener, true);
  }

  addEventListeners(body) {
    body.addEventListener('click', this.clickListener, true);
    body.addEventListener('contextmenu', this.clickListener, true);
  }

  isVisible(elem, container) {
    const elemRect = Offset(elem);
    const containerRect = Offset(container);
    if (2 < containerRect.top - elemRect.bottom) {
      return false;
    }
    if (2 < elemRect.top - containerRect.bottom) {
      return false;
    }
    if (2 < elemRect.left - containerRect.right) {
      return false;
    }
    if (2 < containerRect.left - elemRect.right) {
      return false;
    }
    return true;
  }
}
