import { ChangeDetectorRef, Directive, ElementRef, EventEmitter, forwardRef, <PERSON><PERSON><PERSON>, OnDestroy } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

import { fromEvent, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Directive({
  selector: 'input[autoFillTrigger], textarea[autoFillTrigger]',
  exportAs: 'autoFillTrigger',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => AutoFillTriggerDirective),
      multi: true,
    },
  ],
})
export class AutoFillTriggerDirective implements OnDestroy, ControlValueAccessor {
  value: any;
  onChange = (_) => {};
  onTouched = (_) => {};

  onFocusin: EventEmitter<any> = new EventEmitter();
  onBlur: EventEmitter<any> = new EventEmitter();
  onInput: EventEmitter<any> = new EventEmitter();
  onKeydown: EventEmitter<any> = new EventEmitter();
  onClick: EventEmitter<any> = new EventEmitter();

  destroy$: Subject<boolean> = new Subject<boolean>();

  focus(caretPos = null) {
    this.el.nativeElement.focus();
    this.el.nativeElement.setSelectionRange(caretPos, caretPos);
  }

  insertText(suggestion) {
    const value = this.el.nativeElement.value;
    const insertValue = suggestion.text;
    const newValue = [value.slice(0, suggestion.startColumn), insertValue, value.slice(suggestion.endColumn, value.length)].join('');

    this.el.nativeElement.value = newValue;
    this.focus(suggestion.startColumn + insertValue.length + 1);

    this.onChange(newValue);
    this.value = newValue;
  }

  constructor(private ngZone: NgZone, public el: ElementRef, private ref: ChangeDetectorRef) {}

  ngOnInit() {}

  writeValue(value) {
    this.value = value;
    if (typeof value === 'string') {
      this.el.nativeElement.value = value;
    } else {
      this.el.nativeElement.value = '';
    }
  }
  registerOnChange(fn) {
    this.onChange = fn;
  }
  registerOnTouched(fn) {
    this.onTouched = fn;
  }

  ngAfterViewInit() {
    this.setupEventListener('blur', this.onBlur);
    this.setupEventListener('focusin', this.onFocusin);
    this.setupEventListener('input', this.onInput, true);
    this.setupEventListener('click', this.onClick, true);
    this.setupEventListener('keydown', this.onKeydown, true);
  }

  completeEvents() {
    this.destroy$.next(true);
    this.destroy$.complete();

    this.onFocusin.complete();
    this.onBlur.complete();
    this.onInput.complete();
    this.onKeydown.complete();
    this.onClick.complete();
  }

  ngOnDestroy() {
    this.completeEvents();
  }

  setupEventListener(eventName, eventEmitter, shouldPassEvent = false) {
    this.ngZone.runOutsideAngular(() => {
      fromEvent(this.el.nativeElement, eventName)
        .pipe(takeUntil(this.destroy$))
        .subscribe((event: any) => {
          if (eventEmitter.observers.length) {
            this.ngZone.run(() => {
              eventEmitter.emit(shouldPassEvent ? event : undefined);

              if (eventName == 'input') {
                this.onChange(event.target.value);
              }
              this.ref.markForCheck();
            });
          }
        });
    });
  }
}
