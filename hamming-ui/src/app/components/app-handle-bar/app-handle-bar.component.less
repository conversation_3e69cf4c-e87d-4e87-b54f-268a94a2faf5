@import url("~@tui/component-library/src/style/mixin/mixin.less");

.handle-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 8px 0;
  font-size: 14px;
  color: #62768a;
  background-color: #f3f4f7;
  box-shadow: 0 3px 3px 0 rgb(223 223 223 / 18%);

  .tips-title {
    position: relative;
    display: flex;
    align-content: center;
    align-items: center;
    min-width: 240px;
    height: 40px;
    padding: 10px;
    font-size: 14px;
    font-weight: bold;
    color: #444;
    background-size: 100% 100%;

    i.tips-icon  {
      display: flex;
      margin-right: 6px;

      svg{
        width: 16px;
        height: 16px;
        fill: @primary-color;
      }

    }

    span.tips-titlespan {
      position: absolute;
      right: 10px;
      width: 5px;
      height: 39px;
      background: #fff;
      border-left: 1px solid #ffa7a4;
      transform: skew(344deg);
    }

    .tips-titletwo {
      position: absolute;
      top: -1px;
      right: -6px;
      width: 12px;
      height: 39px;
      background: #fff;
      border-top: 1px solid #e5e5e5;
      border-left: 1px solid #ffa7a4;
      transform: skew(344deg);
    }


  }

  .tips-inner {
    display: flex;
    justify-content: space-between;
    width: calc(100% - 240px);
    height: 40px;
    padding-right: 4px;
    overflow: hidden;
    line-height: 40px;
    background-color: #fff;
    border: 1px solid #e5e5e5;
    border-left: none;
  }

  span.tips-span {
    margin: 0 4px;
    color: #c3986b;
  }

  .tips-btn {
    display: flex;
    align-items: center;

    button,
    t-double-calendar {
      margin-left: 8px;
    }
  }
}

:host-context([ui-theme="defaultTheme"], [ui-theme^="cusTheme"]) {
  .handle-box .tips-title {
    background: -webkit-gradient(
      linear,
      left left,
      left right,
      color-stop(0%, rgb(255 255 255 / 100%)),
      color-stop(60%, rgb(255 255 255 / 100%)),
      color-stop(88%, rgb(255 191 187 / 47%)),
      color-stop(100%, rgb(255 191 187 / 68%))
    );
    background: linear-gradient(
      to right,
      rgb(255 255 255 / 100%) 0%,
      rgb(255 255 255 / 100%) 60%,
      rgb(255 191 187 / 47%) 88%,
      rgb(255 191 187 / 68%) 100%
    );
    background: linear-gradient(
      to right,
      rgb(255 255 255 / 100%) 0%,
      rgb(255 255 255 / 100%) 60%,
      rgb(255 191 187 / 47%) 88%,
      rgb(255 191 187 / 68%) 100%
    );
    border: 1px solid;
    border-right: none;
    border-image: linear-gradient(to right, #e6aaa7, #e6c4c2, #e6e6e6) 1 1;
  }
}

:host-context([ui-theme="blueTheme"]) {
  .handle-box .tips-title {
    background: -webkit-gradient(
      linear,
      left left,
      left right,
      color-stop(0%, rgb(255 255 255 / 100%)),
      color-stop(50%, rgb(255 255 255 / 100%)),
      color-stop(88%, rgb(214 247 255 / 47%)),
      color-stop(100%, rgb(214 247 255 / 98%))
    );
    background: linear-gradient(
      to right,
      rgb(255 255 255 / 100%) 0%,
      rgb(255 255 255 / 100%) 50%,
      rgb(214 247 255 / 47%) 88%,
      rgb(214 247 255 / 98%) 100%
    );
    background: linear-gradient(
      to right,
      rgb(255 255 255 / 100%) 0%,
      rgb(255 255 255 / 100%) 50%,
      rgb(214 247 255 / 47%) 88%,
      rgb(214 247 255 / 98%) 100%
    );
    border: 1px solid;
    border-right: none;
    border-image: linear-gradient(to right, #accaea, #c8d7e8, #e6e6e6) 1 1;

    span.tips-titlespan {
      border-left: 1px solid #76d9f2;
    }

    .tips-titletwo {
      border-left: 1px solid #76d9f2;
    }
  }
}

@media only screen and (max-width: 1366px) {
  .handle-box {
    min-width: 1166px;
  }
}

@media only screen and (min-width: 1366px) and(max-width: 1570px) {
  .handle-box {
    min-width: 1166px;
  }
}
