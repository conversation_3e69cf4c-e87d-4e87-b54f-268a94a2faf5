<div class="handle-box">
  <div class="tips-title" [ngStyle]="{ 'background-size': '100% 100%' }">
    <span class="tips-titlespan"></span>
    <span class="tips-titletwo"></span>
    <i class="tips-icon">
      <svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 200 200" xml:space="preserve">
        <path
          d="M153.1,117.6c6.7,0,12.8,3.6,16.1,9.4l11.1,19.4c3.3,5.7,3.3,12.7,0,18.4l-11.1,19.4c-3.3,5.8-9.5,9.4-16.1,9.4h-21.8
   c-6.7,0-12.8-3.6-16.1-9.4l-11.1-19.4c-3.3-5.7-3.3-12.7,0-18.4l11.1-19.4c3.3-5.8,9.5-9.4,16.1-9.4H153.1z M101.7,8.8L161.5,44
   c7.7,4.5,10.3,14.5,5.8,22.3c-1.4,2.4-3.4,4.4-5.8,5.8l-8.4,4.9l8.4,4.9c7.7,4.5,10.3,14.5,5.8,22.3c-1,1.7-2.3,3.2-3.9,4.5
   c-2.8-1.1-5.8-1.7-8.8-1.8l-1.5,0h-18.2l18.4-10.8L137,86.4l-35.4,20.8c-5.1,3-11.4,3-16.5,0L49.8,86.4l-16.2,9.5l59.9,35.2
   l10.4-6.1l-9.1,16c-1.2,2-2.1,4.2-2.7,6.3c-2.4-0.2-4.7-0.9-6.8-2.2l-35.4-20.8l-16.2,9.5L93.4,169l0.5-0.3l0.4,0.7l0.4,0.7
   l7.3,12.7l-0.3,0.2c-5.1,3-11.4,3-16.5,0l-59.9-35.2c-7.7-4.5-10.3-14.5-5.8-22.3c1.4-2.4,3.4-4.4,5.8-5.8l8.4-5l-8.4-4.9
   c-7.7-4.5-10.3-14.5-5.8-22.3c1.4-2.4,3.4-4.4,5.8-5.8l8.4-4.9L25.3,72c-7.7-4.5-10.3-14.5-5.8-22.3c1.4-2.4,3.4-4.4,5.8-5.8
   L85.2,8.8C90.3,5.8,96.6,5.8,101.7,8.8L101.7,8.8z M153.1,133.8h-21.8c-0.8,0-1.6,0.4-2,1.2l-11.1,19.4c-0.4,0.7-0.4,1.6,0,2.3
   l11.1,19.4c0.4,0.7,1.2,1.2,2,1.2h21.8c0.8,0,1.6-0.4,2-1.2l11.1-19.4l0.2-0.4c0.2-0.6,0.1-1.3-0.2-1.9L155.1,135
   C154.7,134.3,153.9,133.8,153.1,133.8z M142.2,144.7c6,0,10.8,4.8,10.8,10.8c0,6-4.8,10.8-10.8,10.8c0,0,0,0,0,0
   c-6,0-10.8-4.9-10.8-10.8C131.4,149.5,136.2,144.7,142.2,144.7z M93.4,22.8L33.5,58l59.9,35.2L153.3,58
   C153.3,58,93.4,22.8,93.4,22.8z"
        />
      </svg>
    </i>
    {{ title }}
  </div>
  <div class="tips-inner">
    <div class="breadcrumb-box">
      <ng-content select="[breadcrumb]"></ng-content>
    </div>
    <div class="tips-btn">
      <ng-content select="[tips-btn]"></ng-content>
    </div>
  </div>
</div>
