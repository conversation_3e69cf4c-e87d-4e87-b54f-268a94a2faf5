import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { BreadcrumbModule, ButtonModule, CalendarModule, TitleModule, TooltipModule } from '@tui/component-library';

import { AppHandleBarComponent } from './app-handle-bar.component';

@NgModule({
  declarations: [AppHandleBarComponent],
  imports: [ReactiveFormsModule, CommonModule, FormsModule, ButtonModule, TitleModule, CalendarModule, TooltipModule, BreadcrumbModule],
  exports: [AppHandleBarComponent],
})
export class AppHandleBarModule {}
