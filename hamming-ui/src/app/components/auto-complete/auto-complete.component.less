@import url("~@tui/component-library/src/style/mixin/mixin"); //  引入统一方法与变量

:host {
  position: relative;

  &.ng-dirty.ng-invalid .editor {
    border: 1px solid #f5222d !important;

    &.focused {
      border-color: #f5222d !important;
      box-shadow: 0 0 0 2px rgba(168, 0, 0, 0.15) !important;
    }
  }

  .editor {
    position: relative;
    min-height: 28px;
    z-index: 999;
    overflow: auto;
    border-radius: 2px;
    border: 1px solid @input-border-color;

    &.focused {
      outline: none;
      border: 1px solid @input-border-hover-color;
      box-shadow: 0 0 0 2px @drop-shadow-color;
    }

    &.input-text {
      ::ng-deep .cm-scroller {
        overflow-x: hidden;
      }
    }

    &.disabled {
      color: @disabled-text-color !important;
      cursor: not-allowed !important;
      background-color: @disabled-bg-color !important;
      border-color: @disabled-border-color !important;
      pointer-events: none;
    }
  }

  .placeholder {
    position: absolute;
    top: 0;
    z-index: 0;
    padding: 4px 2px 4px 6px;
    line-height: 22px;
    color: #b2b2b2;
  }

  ::ng-deep .cm-editor {
    border: none;
    color: @text-color;

    &.cm-focused {
      outline: none;
      border: none;
      box-shadow: none;
    }

    .cm-tooltip{
      z-index: 9999;
    }

    .cm-scroller {
      line-height: 20px;
    }

    &.ͼ1 {
      .cm-completionIcon {
        display: none;
      }

      .cm-line {
        padding-left: 4px;
      }

      .cm-completionDetail {
        font-style: normal;
      }

      .cm-diagnostic-error {
        line-height: 22px;
      }
    }

    &.ͼ2 .cm-tooltip-autocomplete ul {
      background-color: #fff;
      font-family: "-apple-system,BlinkMacSystemFont,'Segoe UI','PingFang SC','Hiragino Sans GB','Microsoft YaHei','Helvetica Neue',Helvetica,Arial,sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol'";

      li {
        padding: 4px 10px;
        font-size: 14px;
        line-height: 22px;
        display: flex;

        &[aria-selected] {
          color: #333;
          background-color: #edf6f7;
        }

        .cm-completionIcon {
          display: none;
        }

        .cm-completionLabel {
          flex: 1;
          overflow-x: hidden;
          text-overflow: ellipsis;
          cursor: pointer;
        }
      }
    }
  }
}
