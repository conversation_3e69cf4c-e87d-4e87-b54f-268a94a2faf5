/* eslint-disable prefer-const */
import {
  After<PERSON>iewInit,
  Component,
  <PERSON><PERSON><PERSON><PERSON>,
  ElementRef,
  EventEmitter,
  HostBinding,
  HostListener,
  Injector,
  Input,
  IterableDiffer,
  IterableDiffers,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
  forwardRef,
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR, NgControl } from '@angular/forms';

import { syntaxTree } from '@codemirror/language';
import { EditorView, keymap, highlightSpecialChars } from '@codemirror/view';
import { EditorState, StateEffect, Text, Compartment } from '@codemirror/state';
import { linter, nextDiagnostic } from '@codemirror/lint';
import { defaultHighlightStyle, syntaxHighlighting } from '@codemirror/language';
import { autocompletion, CompletionContext, pickedCompletion, closeBrackets, startCompletion, Completion } from '@codemirror/autocomplete';

import { code } from 'src/app/common/lang-code/code';
import { TemplateParser, fieldSelection, translateDiagnostic } from 'src/app/common/lang-code/lint';
import { Snippet } from 'src/app/common/lang-code/snippet';

const smokeEffect = StateEffect.define(undefined);
export interface CompletionOption extends Completion {
  paramTypes?: Array<string>;
  [key: string]: any;
}

@Component({
  selector: 'auto-complete',
  templateUrl: './auto-complete.component.html',
  styleUrls: ['./auto-complete.component.less'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => AutoCompleteComponent),
      multi: true,
    },
  ],
})
export class AutoCompleteComponent implements ControlValueAccessor, OnInit, DoCheck, AfterViewInit, OnDestroy {
  @ViewChild('editor', { static: true }) editor: ElementRef;

  @Input() type: 'input' | 'textarea' = 'input';

  @Input() placeholder = '';

  @Input() maxlength = Infinity;

  @Input() value = '';

  @Input() disabled = false;

  @Input() set suggestions(value) {
    if (!this._differ && value) {
      this._differ = this._differs.find(value).create();
    }
    this._suggestions = value;
  }

  get suggestions() {
    return this._suggestions;
  }

  @Output() validateChange: EventEmitter<boolean> = new EventEmitter();

  hasError = false;
  focusing = false;

  editView;

  onChange = (_) => {};
  onTouched = (_) => {};

  public onModelChange: Function = () => {};
  public onModelTouched: Function = () => {};

  private _control: NgControl;

  private _differ: IterableDiffer<any> | null = null;

  private _suggestions = [];

  private compartment: Compartment;

  @HostBinding('class.ng-invalid')
  get invalid() {
    return this._control?.invalid || this.hasError;
  }

  constructor(private _injector: Injector, private _differs: IterableDiffers) {}

  ngOnInit(): void {
    this._control = this._injector.get(NgControl);

    this.initView();
  }

  ngDoCheck(): void {
    if (this._differ) {
      const changes = this._differ.diff(this._suggestions);

      if (changes) {
        if (this.editView) {
          this.editView.dispatch({
            effects: this.compartment.reconfigure([linter(this.verify.bind(this))]),
          });
        }
      }
    }
  }

  ngAfterViewInit(): void {}

  ngOnDestroy(): void {
    this.editView.destroy();
  }

  /**
   *  获取焦点
   */
  @HostListener('click', ['$event'])
  onClick(event) {
    if (!this.disabled && !this.focusing) {
      this.editView?.focus();
    }
  }

  writeValue(value: any): void {
    this.value = value;
    this.editView.dispatch({
      changes: { from: 0, to: this.editView.state.doc.length, insert: value },
    });
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  /**
   * 初始化state
   */
  initView() {
    this.compartment = new Compartment();
    const extensions = [
      code(),
      closeBrackets(),
      highlightSpecialChars(),
      syntaxHighlighting(defaultHighlightStyle, { fallback: true }),
      this.compartment.of([linter(this.verify.bind(this))]),
      EditorState.transactionFilter.of((tr) => {
        return tr.newDoc.lines > 1 && this.type == 'input' ? [] : [tr];
      }),
      EditorState.readOnly.of(this.disabled),
      autocompletion({
        activateOnTyping: true,
        aboveCursor: true,
        override: [this.completions.bind(this)],
        maxRenderedOptions: 1000
      }),
      EditorView.inputHandler.of(this.limitLength.bind(this)),
      EditorView.focusChangeEffect.of((_, focusing) => {
        this.focusing = focusing;
        return smokeEffect.of(undefined);
      }),
      EditorView.domEventHandlers({
        click: () => {
          setTimeout(() => {
            startCompletion(this.editView);
          }, 200);
        },
        paste: this.paste.bind(this),
      }),
      EditorView.updateListener.of(({ docChanged, state, view }) => {
        if (docChanged) {
          const len = state.doc.length;
          this.value = state.sliceDoc(0, len);
          this.onChange(this.value);
        }
      }),
      keymap.of([{ any: this.toggle.bind(this) }]),
    ];

    if (this.type == 'textarea') {
      extensions.push(EditorView.lineWrapping);
    }

    this.editView = new EditorView({
      doc: this.value,
      extensions: extensions,
      parent: this.editor.nativeElement,
    });
  }

  /**
   * 自动提示
   * @param context
   * @param args
   * @returns
   */
  completions(context: CompletionContext, ...args) {
    const that = this;
    // 获取当前位置节点信息
    const inner = syntaxTree(context.state).resolveInner(context.pos);

    if (inner.name !== 'Template' && inner.name !== 'InnerString') {
      return null;
    }

    let offset = 0;
    let txt;

    // 父级是模版直接显示function列表
    if (inner.name == 'Template') {
      txt = context.state.doc.sliceString(inner.from, inner.to);
      offset = inner.from;
    } else if (inner.name == 'InnerString') {
      txt = context.state.doc.sliceString(inner.parent.from, inner.parent.to);
      offset = inner.parent.from;
    }

    // 解析
    const result = new TemplateParser(txt).parse();

    const node = resolveNode(result[0], context.pos - offset, offset);

    function snippet(template) {
      const snippet = Snippet.parse(template);
      return (editor, completion, start, end) => {
        const { text, ranges } = snippet.instantiate(editor.state, start);
        const spec: any = {
          changes: { from: start, to: node.to, insert: Text.of(text) },
          scrollIntoView: true,
          annotations: completion ? pickedCompletion.of(completion) : undefined,
        };

        if (ranges.length) spec.selection = fieldSelection(ranges, 0);

        editor.dispatch(editor.state.update(spec));

        if (node.type == 'function') {
          setTimeout(() => {
            startCompletion(that.editView);
          }, 200);
        }
      };
    }

    let options = [];
    if (node.type == 'function') {
      options = this.suggestions
        .filter((el) => el.type == 'function')
        .map((el) => ({
          ...el,
          apply: snippet(`${el.label}(\$\{\})`),
        }));
    } else if (node.type == 'arguments' && node.index == 0) {
      const completion = this.suggestions.find((el) => el.type == 'function' && el.label == node.callee.value.trim());

      if (completion) {
        if (!completion.supportArray) {
          options = this.suggestions.filter((el) => !el.isArray);
        }

        options = this.suggestions
          .filter((el) => completion.paramTypes[0].includes(el.type))
          .map((el) => ({
            ...el,
            apply: snippet(el.label),
          }));
      }
    }

    // 忽略空格
    let char = context.state.doc.sliceString(node.from, node.from + 1);
    while (node.from + 1 <= context.pos && char == ' ') {
      node.from += 1;
      char = context.state.doc.sliceString(node.from, node.from + 1);
    }

    return {
      from: node.from,
      options,
    };
  }

  toggle(view: EditorView, event) {
    // 忽略复合按键
    if (event.key == 'ArrowUp' || event.key == 'ArrowDown' || event.key == 'ArrowLeft' || event.key == 'ArrowRight') {
      console.log(event.key);
      setTimeout(() => {
        startCompletion(this.editView);
      }, 200);
    }
    return false;
  }

  /**
   * 规则校验
   * @returns
   */
  runRules(ast) {
    const found = [];
    ast.forEach((template) => {
      const expressions = template.children;
      const len = expressions.length;
      if (0 == len) {
        found.push(translateDiagnostic(template, `模板不能为空`));
      } else if (1 < len) {
        found.push(translateDiagnostic(template, `模板内只能引用一个函数或变量`));
      } else {
        const expr = expressions[0];
        if (expr.type == 'Identifier') {
          const field = this.suggestions.find((el) => el.label == expr.value.trim());
          if (!field) {
            found.push(translateDiagnostic(expr, `引用的变量不存在`));
          }
        } else {
          const func = this.suggestions.filter((el) => el.type == 'function').find((el) => el.label == expr.callee.value.trim());
          if (!func) {
            found.push(translateDiagnostic(expr, `引用的的函数不存在`));
          } else if (expr.arguments.length == 0) {
            found.push(translateDiagnostic(expr, `函数参数不能为空`));
          } else if (expr.arguments.length > 1) {
            found.push(translateDiagnostic(expr, `函数只支持一个参数`));
          } else {
            const args = expr.arguments[0];
            const field = this.suggestions.find((el) => el.label == args.value.trim());
            if (!field || !func.paramTypes[0].includes(field.type)) {
              found.push(translateDiagnostic(args, `函数参数不支持`));
            }
          }
        }
      }
    });
    return found;
  }

  /**
   * 文本验证
   * @param view
   * @returns
   */
  verify(view: EditorView) {
    const { state } = view;

    const len = state.doc.length;
    const text = state.sliceDoc(0, len);

    // 生成ast树
    const astTree = new TemplateParser(text).parse();

    // 解析ast树,进行规则校验
    const lintingProblems = this.runRules(astTree);

    this.hasError = lintingProblems.length !== 0;

    this.validateChange.emit(this.hasError);
    return lintingProblems;
  }

  /**
   * 处理Ctrl-V事件
   * @param view
   * @param event
   */
  paste(event, view) {
    if (view.state.readOnly) return true;
    const len = view.state.doc.length;
    let select: any =  '';
    let value = view.state.sliceDoc(0, len);
    let data = event.clipboardData;
    let txt = data.getData('text/plain') || data.getData('text/uri-text');
    txt = txt.slice(0, this.maxlength - len);
    // 判断是否选中了文本框文字
    try {
      select = document.getSelection();
    } catch (error) {
      select = {anchorOffset: len, focusOffset: len};
    }
    if(select.anchorOffset + select.focusOffset == 0) {
      value = txt + value;
    } else if(select.anchorOffset - select.focusOffset == 0) {
      value = value +  txt;
    } else {
      const s = Math.min(select.anchorOffset, select.focusOffset), e = Math.max(select.anchorOffset, select.focusOffset)
      value = value.substr(0, s) + txt + value.substr(e, value.length);
    }
    
    

    let transaction = view.state.update({ changes: { from: 0, to: view.state.doc.length, insert: value } });
    view.dispatch(transaction);
    // this.onModelChange(value)
    return true;
  }

  /**
   * 限制长度
   * @param view
   * @param from
   * @param to
   * @param text
   * @returns
   */
  limitLength(view, from, to, text) {
    // 限制长度
    const len = view.state.doc.length;
    if (text && len >= this.maxlength) {
      // text = text.slice(0, this.maxlength - len);
      return true;
    } else {
      return false;
    }
  }
}

/**
 * 获取节点
 * @param node
 * @param pos
 * @param side
 * @param overlays
 * @returns
 */
function resolveNode(node, pos, offset) {
  // 获取节点
  const inner = node.enter(pos);

  // 判断callExpression
  if (inner.type == 'CallExpression') {
    if (pos >= inner.callee.start && pos <= inner.callee.end) {
      return {
        from: inner.start + offset,
        to: inner.end + offset,
        type: 'function',
      };
    }

    for (let i = 0, j = inner.arguments.length; i < j; i++) {
      const argument = inner.arguments[i];
      if (argument.end >= pos && argument.start <= pos) {
        return {
          from: argument.start + offset,
          to: argument.end + offset,
          type: 'arguments',
          callee: inner.callee,
          index: i,
        };
      }
    }

    return {
      from: inner.callee.end + 1 + offset,
      to: inner.end - 1 + offset,
      type: 'arguments',
      callee: inner.callee,
      index: 0,
    };
  } else if (inner.type == 'TemplateLiteral') {
    return {
      from: inner.start + 2 + offset,
      to: inner.end - 1 + offset,
      type: 'function',
    };
  } else {
    return {
      from: inner.start + offset,
      to: inner.end + offset,
      type: 'function',
    };
  }
}
