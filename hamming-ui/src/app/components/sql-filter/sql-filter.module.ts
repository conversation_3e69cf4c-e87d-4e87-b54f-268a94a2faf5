import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { OptionsInputModule, ButtonModule, CalendarModule, DropdownModule, InputEnlargeModule, SpinnerModule, TooltipModule } from '@tui/component-library';

import { SqlFilterComponent } from './sql-filter.component';
import { SqlFilterGroupComponent } from './sql-filter-group/sql-filter-group.component';
import { SqlFilterExpressionComponent } from './sql-filter-expression/sql-filter-expression.component';
import { SqlFilterTextEditorComponent } from './editors/sql-filter-text-editor/sql-filter-text-editor.component';
import { SqlFilterIntEditorComponent } from './editors/sql-filter-int-editor/sql-filter-int-editor.component';
import { SqlFilterFuncComponent } from './sql-filter-func/sql-filter-func.component';
import { SqlFilterDateEditorComponent } from './editors/sql-filter-date-editor/sql-filter-date-editor.component';
import { SqlFilterMatchComponent } from './sql-filter-match/sql-filter-match.component';

@NgModule({
  declarations: [SqlFilterComponent, SqlFilterGroupComponent, SqlFilterExpressionComponent, SqlFilterTextEditorComponent, SqlFilterIntEditorComponent, SqlFilterFuncComponent, SqlFilterDateEditorComponent, SqlFilterMatchComponent],
  imports: [CommonModule, FormsModule, DropdownModule, ButtonModule, TooltipModule, CalendarModule, SpinnerModule, OptionsInputModule, InputEnlargeModule],
  exports: [SqlFilterComponent],
  schemas: [],
})
export class SqlFilterModule {}
