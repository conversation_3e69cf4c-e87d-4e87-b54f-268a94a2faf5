<div class="sql-filter-toolbar">
  <div class="sql-filter-toolbar-item">
    <t-dropdown [options]="funcs" [(ngModel)]="currentItem.func" (onChange)="valueChange.emit()" [disabled]="disabled" placeholder="请选择过滤器" [filter]="true" [style]="{ width: '480px' }">
      <ng-template let-item tTemplate="item">
        <ng-container *ngIf="funcRender; else funcTemplate">
          <ng-container *ngTemplateOutlet="funcRender; context: { $implicit: item }"></ng-container>
        </ng-container>
        <ng-template #funcTemplate>
          {{ item.label }}
        </ng-template>
      </ng-template>
    </t-dropdown>
  </div>
  <div class="sql-filter-toolbar-item">
    <i class="icon-close del-icon" (click)="removeFilterExpression()"></i>
  </div>
</div>
