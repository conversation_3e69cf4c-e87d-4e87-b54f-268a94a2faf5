import { Component, EventEmitter, Input, OnInit, Output, TemplateRef } from '@angular/core';

import { OptionType } from 'src/app/models/option';
import { FilterService } from '../filter-service';
import { LeafNode, SqlExpression } from '../model';

@Component({
  selector: 'app-sql-filter-func',
  templateUrl: './sql-filter-func.component.html',
  styleUrls: ['./sql-filter-func.component.less'],
})
export class SqlFilterFuncComponent implements OnInit {
  @Input() funcRender: TemplateRef<any>;
  @Input() currentItem: LeafNode;
  @Input() parentItem: SqlExpression;
  @Input() index: number;
  @Input() disabled: boolean;
  @Output() valueChange = new EventEmitter();

  funcs: OptionType[];

  constructor(private filterService: FilterService) {}

  ngOnInit() {
    // this.funcs = this.filterService.funcs;
  }

  removeFilterExpression() {
    this.filterService.remove(this.currentItem, this.index, this.parentItem);
    this.valueChange.emit();
  }
}
