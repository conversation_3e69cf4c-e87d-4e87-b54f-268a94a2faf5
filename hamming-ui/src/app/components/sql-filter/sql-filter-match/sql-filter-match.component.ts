import { AfterViewInit, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

import { OptionType } from 'src/app/models/option';
import { DataService } from '../data.service';
import { FilterService } from '../filter-service';
import { LeafNode, SqlExpression } from '../model';

@Component({
  selector: 'sql-filter-match',
  styleUrls: ['./sql-filter-match.component.less'],
  templateUrl: './sql-filter-match.component.html',
})
export class SqlFilterMatchComponent implements OnInit, AfterViewInit {
  @Input() currentItem: LeafNode;
  @Input() parentItem: SqlExpression;
  @Input() index: number;
  @Input() disabled: boolean;
  @Output() valueChange = new EventEmitter();

  // 知识库图标
  knowledgeIcon = null;
  filters: OptionType[];

  // 知识库下拉数据集
  knowledges: any[];

  // 匹配字段数据集
  fields: any[];

  get grouped(): boolean {
    return this.dataService.grouped;
  }

  constructor(private dataService: DataService, private filterService: FilterService) {}

  ngOnInit(): void {
    this.filters = this.dataService.data;
    this.knowledges = this.filterService.knowledges;

    // 获取知识库和匹配结果数据
    if (this.currentItem.func) {
      this.fields = this.filterService.findDestFields(this.currentItem.func);
    }
  }

  ngAfterViewInit(): void {
    this.knowledgeIcon = {
      knowledge_filter: {icon:'icon-advanced', text: '知识库匹配'},
      knowledge_drop: {icon:'icon-filter1', text: '知识库过滤'}
    }[this.currentItem.operator];
    
  }

  /**
   * 知识库选择改变事件
   */
  selectChange() {
    this.valueChange.emit();
  }

  funcChange() {
    this.fields = this.filterService.findDestFields(this.currentItem.func);
    this.currentItem.right = '';
    this.valueChange.emit();
  }

  removeFilterExpression() {
    this.filterService.remove(this.currentItem, this.index, this.parentItem);
    this.valueChange.emit();
  }
}
