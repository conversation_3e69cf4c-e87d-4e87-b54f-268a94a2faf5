<div class="sql-filter-toolbar">
  <div class="sql-filter-toolbar-item">
    <t-dropdown
      [options]="filters"
      [(ngModel)]="currentItem.left"
      [layoutModel]="'group-horizontal'"
      [numberMoreLayout]="grouped"
      [morelayoutNumber]="0"
      [filter]="true"
      placeholder="请选择字段"
      [filter]="true"
      [disabled]="disabled"
      [style]="{ width: '145px' }"
      (onChange)="selectChange()"
    >
      <ng-template let-item tTemplate="group">
        <div [tTooltip]="item.label">
          <span class="dropdown-group-title">
            {{ item.label }}
          </span>
          <i class="i-latebox icon-paginato-right"></i>
        </div>
      </ng-template>
    </t-dropdown>
  </div>
  <div class="sql-filter-toolbar-item">
    <t-dropdown [options]="knowledges" [(ngModel)]="currentItem.func" [disabled]="disabled" placeholder="请选择知识库" [layoutModel]="'group-horizontal'" [filter]="true" [style]="{ width: '145px' }" (onChange)="funcChange()"></t-dropdown>
  </div>
  <div class="sql-filter-toolbar-item">
    <t-dropdown class="wd170" [options]="fields" [(ngModel)]="currentItem.right" [disabled]="disabled" placeholder="请选择匹配结果" [filter]="true" [style]="{ width: '170px' }" (onChange)="selectChange()"></t-dropdown>
  </div>
  <div class="sql-filter-toolbar-item" *ngIf="!disabled">
    <i *ngIf="knowledgeIcon" [tTooltip]="knowledgeIcon.text" class="knowledge-icon" [ngClass]="knowledgeIcon.icon"></i>
    <i class="icon-close del-icon" (click)="removeFilterExpression()"></i>
  </div>
</div>
