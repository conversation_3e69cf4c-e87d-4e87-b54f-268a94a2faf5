import { Component, EventEmitter, Input, OnInit, Output, TemplateRef } from '@angular/core';

import { OptionType } from 'src/app/models/option';
import { DataService } from './data.service';
import { FilterService } from './filter-service';
import { OperatorType, SqlExpression } from './model';

@Component({
  selector: 'app-sql-filter',
  templateUrl: './sql-filter.component.html',
  styleUrls: ['./sql-filter.component.less'],
  providers: [FilterService, DataService],
})
export class SqlFilterComponent implements OnInit {
  @Input() funcRender: TemplateRef<any>;

  @Input() showKnowledge = true;

  /**
   * 禁用的字段
   */
  @Input() set invalidFields(value) {
    this.dataService.setInvalidKey(value);
  }

  @Input() set operators(operators: OperatorType[]) {
    this.filterService.operators = operators || [];
  }

  get operators() {
    return this.filterService.operators;
  }

  @Input() set fields(filters: OptionType[]) {
    this.dataService.fields = filters;
  }

  get fields() {
    return this.dataService.fields;
  }

  @Input() set knowledges(knowledges: any[]) {
    this.filterService.knowledges = knowledges || [];
  }

  get knowledges() {
    return this.filterService.knowledges;
  }

  @Input() set destFields(destFields: any[]) {
    this.filterService.destFields = destFields || [];
  }

  get destFields() {
    return this.filterService.destFields;
  }

  @Input() set value(value: SqlExpression) {
    this.filterService.value = value;
  }

  get value() {
    return this.filterService.value;
  }

  @Output() valueChange = new EventEmitter();

  private _invalidFields = [];

  constructor(private dataService: DataService, private filterService: FilterService) {}

  ngOnInit() {}

  onValueChange() {
    this.valueChange.emit(this.filterService.value);
  }
}
