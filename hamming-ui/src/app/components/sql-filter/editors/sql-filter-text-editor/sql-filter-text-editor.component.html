<div class="sql-filter-toolbar-item">
  <t-dropdown [options]="options" [(ngModel)]="currentItem.right" placeholder="请输入" [filter]="true" [showClear]="true" [inputable]="true" (onChange)="onChange($event)" [style]="{ width: '195px' }"> </t-dropdown>

  <!-- <t-optionsInput
    [(ngModel)]="currentItem.right"
    [suggestions]="options"
    (completeMethod)="filterOptions($event)"
    placeholder="请输入"
    [dropdown]="true"
    (onChange)="valueChange.emit(input.value)"
    (onSelect)="valueChange.emit(input.value)"
    #input
  >
  </t-optionsInput> -->
</div>
