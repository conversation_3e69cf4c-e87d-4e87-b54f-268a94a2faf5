import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { LeafNode } from '../../model';

@Component({
  selector: 'app-sql-filter-text-editor',
  templateUrl: './sql-filter-text-editor.component.html',
})
export class SqlFilterTextEditorComponent implements OnInit {
  @Input() currentItem: LeafNode;
  @Input() set suggestions(value) {
    if (Array.isArray(value)) {
      this.options = [...value];
    } else {
      this.options = [];
    }
  }
  @Input() disabled: boolean;
  @Output() valueChange = new EventEmitter();

  options = [];

  constructor() {}

  ngOnInit() {}

  filterOptions(event: any) {
    this.options = [];
    for (let i = 0; i < this.suggestions.length; i++) {
      const brand = this.suggestions[i];
      if (brand.toLowerCase().indexOf(event.query.toLowerCase()) == 0) {
        this.options.push(brand);
      }
    }
  }

  onChange(event) {
    this.valueChange.emit(event.value);
  }
}
