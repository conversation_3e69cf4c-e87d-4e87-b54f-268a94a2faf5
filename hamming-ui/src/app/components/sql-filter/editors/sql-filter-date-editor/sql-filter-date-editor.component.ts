import { DatePipe } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

import { LeafNode } from '../../model';

@Component({
  selector: 'app-sql-filter-date-editor',
  templateUrl: './sql-filter-date-editor.component.html',
  styleUrls: ['./sql-filter-date-editor.component.less'],
  providers: [DatePipe],
})
export class SqlFilterDateEditorComponent implements OnInit {
  @Input() currentItem: LeafNode;
  @Input() disabled: boolean;
  @Output() valueChange = new EventEmitter();

  value: Date;

  constructor(private datePipe: DatePipe) {}

  ngOnInit() {
    if (this.currentItem.right) {
      !this.currentItem.right.toString().split(' ')[1] && (this.currentItem.right += ' 00:00:00');
      this.value = new Date(this.currentItem.right);
    }
  }

  onValueChange(value: any) {
    this.currentItem.right = this.datePipe.transform(value, 'yyyy-MM-dd hh:mm:ss');
    this.valueChange.emit(this.currentItem);
  }
}
