import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

import { LeafNode } from '../../model';

@Component({
  selector: 'app-sql-filter-int-editor',
  templateUrl: './sql-filter-int-editor.component.html',
  styleUrls: ['./sql-filter-int-editor.component.less'],
})
export class SqlFilterIntEditorComponent implements OnInit {
  @Input() currentItem: LeafNode;
  @Input() disabled: boolean;
  @Output() valueChange = new EventEmitter();

  @Input() set suggestions(value) {
    if (Array.isArray(value)) {
      this.options = [...value];
    } else {
      this.options = [];
    }
  }

  options = [];

  constructor() {}

  ngOnInit() {}

  onChange(event) {
    this.valueChange.emit(event.value);
  }
}
