<div class="sql-filter-toolbar">
  <div class="sql-filter-toolbar-item">
    <t-dropdown
      [options]="filters"
      [(ngModel)]="currentItem.left"
      [layoutModel]="'group-horizontal'"
      [numberMoreLayout]="grouped"
      [morelayoutNumber]="0"
      [filter]="true"
      placeholder="请选择字段"
      [filter]="true"
      [disabled]="disabled"
      [style]="{ width: '145px' }"
      (onChange)="filterValueChange($event)"
    >
      <ng-template let-item tTemplate="group">
        <div [tTooltip]="item.label">
          <span class="dropdown-group-title">
            {{ item.label }}
          </span>
          <i class="i-latebox icon-paginato-right"></i>
        </div>
      </ng-template>
    </t-dropdown>
  </div>
  <div class="sql-filter-toolbar-item">
    <t-dropdown [options]="operators" [(ngModel)]="currentItem.operator" [disabled]="disabled" placeholder="请选择条件" [filter]="true" [style]="{ width: '145px' }" (onChange)="operatorValueChange($event)">
      <ng-template let-item tTemplate="group">
        <div [tTooltip]="item.label">
          <span class="dropdown-group-title">
            {{ item.label }}
          </span>
          <i class="i-latebox icon-paginato-right"></i>
        </div>
      </ng-template>
    </t-dropdown>
  </div>
  <ng-container *ngIf="isBinaryExpression()">
    <ng-container [ngSwitch]="getEditorType()">
      <app-sql-filter-text-editor *ngSwitchCase="'string'" [currentItem]="currentItem" (valueChange)="valueChange.emit()" [disabled]="disabled" [suggestions]="suggestions"></app-sql-filter-text-editor>
      <app-sql-filter-int-editor *ngSwitchCase="'int'" [currentItem]="currentItem" (valueChange)="valueChange.emit()" [disabled]="disabled" [suggestions]="suggestions"></app-sql-filter-int-editor>

      <app-sql-filter-date-editor *ngSwitchCase="'datetime'" [currentItem]="currentItem" (valueChange)="valueChange.emit()" [disabled]="disabled"></app-sql-filter-date-editor>
    </ng-container>
  </ng-container>
  <div class="sql-filter-toolbar-item" *ngIf="!disabled">
    <i class="icon-close del-icon" (click)="removeFilterExpression()"></i>
  </div>
</div>
