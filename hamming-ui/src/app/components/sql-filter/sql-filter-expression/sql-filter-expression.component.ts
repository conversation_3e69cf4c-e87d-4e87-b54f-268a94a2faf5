import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

import { OptionType } from 'src/app/models/option';
import { DataService } from '../data.service';
import { FilterService } from '../filter-service';
import { LeafNode, SqlExpression } from '../model';

@Component({
  selector: 'app-sql-filter-expression',
  templateUrl: './sql-filter-expression.component.html',
  styleUrls: ['./sql-filter-expression.component.less'],
})
export class SqlFilterExpressionComponent implements OnInit {
  @Input() currentItem: LeafNode;
  @Input() parentItem: SqlExpression;
  @Input() index: number;
  @Input() disabled: boolean;
  @Output() valueChange = new EventEmitter();

  get filters(): OptionType[] {
    return this.dataService.data;
  }

  suggestions: [];
  operators: any[];

  get grouped(): boolean {
    return this.dataService.grouped;
  }

  constructor(private dataService: DataService, private filterService: FilterService) {}

  ngOnInit() {
    const foundFilter = this.getFilterExpressionByField(this.currentItem.left);
    if (this.currentItem.left) {
      this.setOperators(foundFilter);

      this.suggestions = foundFilter.suggestions || [];
    }

    const defaultFilter = this.dataService.itemAt(0);
    if (!this.currentItem.left && defaultFilter) {
      // this.currentItem.left = defaultFilter.value;
      this.setOperators(defaultFilter);
    }
  }

  getFilterExpressionByField(field: string) {
    const predicate = (filter) => filter.value === field;
    const foundFilter = this.dataService.find(predicate);

    if (foundFilter) {
      return foundFilter;
    }
    return null;
  }

  setOperators(filter: OptionType) {
    if (filter && filter.type) {
      this.operators = this.filterService.getNormalizeOperators(filter.type);
      // if (!this.currentItem.operator && this.operators.length) {
      //   this.currentItem.operator = this.operators[0].value;
      // }
    }
  }

  isBinaryExpression() {
    const item = this.filterService.operators.find((operator) => operator.value === this.currentItem.operator);

    return item && item.type == 'binary' ? true : false;
  }

  getEditorType() {
    const item = this.dataService.find((filter) => filter.value === this.currentItem.left);
    return item ? item!.type : '';
  }

  filterValueChange({ value }: any) {
    this.currentItem.right = null;
    this.currentItem.left = value;
    const foundFilter = this.getFilterExpressionByField(this.currentItem.left);
    this.suggestions = foundFilter.suggestions || [];
    this.setOperators(foundFilter);
    this.valueChange.emit();
  }

  operatorValueChange({ option }: any) {
    if (option.type == 'unary') {
      this.currentItem.right = '';
    }

    // this.suggestions = field.items
    this.valueChange.emit();
  }

  removeFilterExpression() {
    this.filterService.remove(this.currentItem, this.index, this.parentItem);
    this.valueChange.emit();
  }
}
