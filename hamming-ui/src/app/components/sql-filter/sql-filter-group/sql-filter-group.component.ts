import { Component, EventEmitter, Input, OnInit, Output, TemplateRef } from '@angular/core';

import { TuiAppService } from '@tui/frame';

import { OptionType } from 'src/app/models/option';
import { FilterService } from '../filter-service';
import { SqlExpression } from '../model';

@Component({
  selector: 'app-sql-filter-group',
  templateUrl: './sql-filter-group.component.html',
  styleUrls: ['./sql-filter-group.component.less'],
})
export class SqlFilterGroupComponent implements OnInit {
  @Input() funcRender: TemplateRef<any>;
  @Input() currentItem: SqlExpression;
  @Input() parentItem: SqlExpression;
  @Input() index: number;

  @Output() valueChange = new EventEmitter();
  @Output() currentItemChange = new EventEmitter();

  expand = true;
  @Input() showKnowledge = true;

  operators: OptionType[] = [
    {
      label: 'AND',
      value: 'and',
    },
    {
      label: 'OR',
      value: 'or',
    },
  ];

  constructor(private tuiAppService: TuiAppService, private filterService: FilterService) {
    this.currentItem = {
      operator: 'and',
      children: [
        {
          leaf_node: {
            left: null,
            operator: null,
            right: null,
          },
        },
      ],
    };
  }

  ngOnInit() {}

  toggleExpand() {
    this.expand = !this.expand;
  }

  selectedChange({ value }: any) {
    if (this.currentItem.operator !== value) {
      this.currentItem.operator = value;
      this.valueChange.emit();
    }
  }

  addFilterExpression() {
    this.filterService.addFilterExpression(this.currentItem);
    this.valueChange.emit();
  }

  // type ? drop: 知识库排除  filter:知识库匹配
  addFilterFunc(type = 'filter') {
    this.filterService.addFilterRule(this.currentItem, `knowledge_${type}`);
    this.valueChange.emit();
  }
  addFilterGroup() {
    this.filterService.addFilterGroup(this.currentItem);
    this.valueChange.emit();
  }

  removeFilterGroup() {
    this.filterService.remove(this.currentItem, this.index, this.parentItem);
    this.valueChange.emit();
  }
}
