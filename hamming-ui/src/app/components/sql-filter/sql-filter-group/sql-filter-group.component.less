:host {
  ::ng-deep .sql-filter-toolbar {
    display: flex;
    align-items: center;
    padding: 5px 0;

    .sql-filter-toolbar-item {
      display: flex;
      align-items: center;
      margin: 0 5px;

      t-dropdown {
        display: flex;
      }
    }
  }

  .icon-treeleft {
    display: inline-block;
    margin-right: 4px;
    font-size: 18px;
    color: #1a6082;

    &.expand {
      transition: 0.3s;
      transform: rotate(45deg);
    }
  }

  .sql-filter-lines {
    position: relative;
    padding: 0 0 0 43px;

    &::before {
      position: absolute;
      top: 6px;
      left: 33px;
      width: 1px;
      height: calc(100% - 12px);
      content: " ";
      background: #e4e4e4;
    }
  }
}
