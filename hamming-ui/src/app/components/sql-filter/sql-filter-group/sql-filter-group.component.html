<div class="sql-filter-toolbar">
  <div class="sql-filter-toolbar-item">
    <i class="icon-treeleft" [class.expand]="expand" (click)="toggleExpand()"></i>
    <t-dropdown [options]="operators" [(ngModel)]="currentItem.operator" (onChange)="selectedChange($event)" [style]="{ width: '155px' }"></t-dropdown>
  </div>
  <div class="sql-filter-toolbar-item">
    <button tButton type="button" class="ui-button-secondary iconfont icon-plus" (click)="addFilterExpression()"> 添加条件 </button>
  </div>
  <div class="sql-filter-toolbar-item" *ngIf="showKnowledge">
    <button tButton type="button" class="ui-button-secondary iconfont icon-advanced" tTooltip="10分钟更新一次知识库" tooltipPosition="top" (click)="addFilterFunc('filter')"> 知识库匹配 </button>
  </div>
  <div class="sql-filter-toolbar-item" *ngIf="showKnowledge">
    <button tButton type="button" class="ui-button-secondary iconfont icon-filter1" tTooltip="10分钟更新一次知识库" tooltipPosition="top" (click)="addFilterFunc('drop')"> 知识库过滤 </button>
  </div>
  <div class="sql-filter-toolbar-item">
    <button tButton type="button" class="ui-button-secondary iconfont icon-addGroups" (click)="addFilterGroup()"> 添加条件组 </button>
  </div>
  <div class="sql-filter-toolbar-item" *ngIf="parentItem">
    <i class="icon-close del-icon" (click)="removeFilterGroup()"></i>
  </div>
</div>
<ng-content select="[slot]"></ng-content>
<ul class="sql-filter-lines" *ngIf="expand">
  <ng-container *ngFor="let item of currentItem.children; let i = index">
    <li class="sql-filter-item" *ngIf="item.leaf_node">
      <!-- 过滤条件选择 -->
      <app-sql-filter-expression *ngIf="item.leaf_node.func == null" [currentItem]="item.leaf_node" [parentItem]="currentItem" (valueChange)="valueChange.emit()" [index]="i"> </app-sql-filter-expression>
      <!--  过滤规则选择 -->
      <!-- <app-sql-filter-func
        *ngIf="item.leaf_node.func != null"
        [currentItem]="item.leaf_node"
        [parentItem]="currentItem"
        (valueChange)="valueChange.emit()"
        [index]="i"
        [funcRender]="funcRender"
      >
      </app-sql-filter-func> -->

      <!--  -->
      <sql-filter-match *ngIf="item.leaf_node.func != null" [currentItem]="item.leaf_node" [parentItem]="currentItem" (valueChange)="valueChange.emit()" [index]="i"></sql-filter-match>
    </li>
    <li class="sql-filter-item" *ngIf="item.node">
      <app-sql-filter-group [currentItem]="item.node" [parentItem]="currentItem" (valueChange)="valueChange.emit()" [index]="i" [showKnowledge]="showKnowledge"></app-sql-filter-group>
    </li>
  </ng-container>
</ul>
