import { Injectable } from '@angular/core';

import { OptionType } from 'src/app/models/option';
import { IsPresent } from 'src/app/utils';

@Injectable()
export class DataService {
  grouped = false;

  set fields(value: OptionType[]) {
    this._fields = value || [];
    this.grouped = this.isGrouped(this._fields);

    const filter = (el) => !this.invalidKey.includes(el.value);
    this.filter(filter);
  }

  get fields() {
    return this._fields;
  }

  data: OptionType[] = [];

  flatData: OptionType[] = [];

  private _fields = [];

  private invalidKey = [];

  get itemsCount() {
    if (!IsPresent(this.data) || this.data.length === 0) {
      return 0;
    }
    const items = this.grouped ? this.flatData : this.data;
    return items.length;
  }

  constructor() {
    this.grouped = false;
  }

  isGrouped(data) {
    return IsPresent(data) && data.length !== 0 && IsPresent(data[0]) && IsPresent(data[0].items);
  }

  find(predicate) {
    if (this.grouped) {
      return this.flatData.find(predicate);
    } else {
      return this.data.find(predicate);
    }
  }

  itemAt(index) {
    let dataItem;
    if (this.itemsCount === 0) {
      return dataItem;
    }

    if (this.grouped) {
      dataItem = this.flatData[index];
    } else {
      dataItem = this.data[index];
    }

    return dataItem;
  }

  flatten(data, group = undefined, offset = 0, groupIndex = 0) {
    let flat = [];
    for (let i = 0; i < data.length; i++) {
      let result = [];
      if (data[i].items) {
        result = this.flatten(data[i].items, data[i].value, offset, i);
      } else {
        result.push({
          ...data[i],
          label: data[i].label,
          value: data[i].value,
          type: data[i].type,
        });
      }
      flat = flat.concat(result);
    }
    return flat;
  }

  /**
   * 屏蔽的数据
   */
  setInvalidKey(value) {
    this.invalidKey = value || [];

    const filter = (el) => !this.invalidKey.includes(el.value);
    this.filter(filter);
  }

  filter(filter) {
    const result = [];
    this._fields.forEach((item) => {
      if (!this.invalidKey.includes(item.value)) {
        const items = item.items.filter(filter);
        if (items.length) {
          result.push({
            ...item,
            items,
          });
        }
      }
    });
    this.data = result;

    if (this.grouped) {
      const fields = this.flatten(this._fields);
      this.flatData = fields.filter(filter);
    }
  }
}
