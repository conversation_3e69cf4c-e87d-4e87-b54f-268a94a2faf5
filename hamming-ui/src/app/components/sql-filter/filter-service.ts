import { OptionType } from 'src/app/models/option';
import { FilterEditor, OperatorType, SqlExpression } from './model';

export class FilterService {
  value: SqlExpression = {
    operator: 'and',
    children: [
      {
        leaf_node: {
          left: '',
          operator: '',
          right: '',
        },
      },
    ],
  };

  operators: OperatorType[] = [];

  /**
   * 过滤器
   */
  knowledges: OptionType[] = [];
  destFields: OptionType[] = [];

  isEditorDisabled = false;

  constructor() {}

  addFilterGroup(item: SqlExpression) {
    const filterGroup = {
      node: {
        operator: 'and',
        children: [
          {
            leaf_node: {
              left: '',
              operator: '',
              right: '',
            },
          },
        ],
      },
    };

    item.children.push(filterGroup);
  }

  addFilterRule(item: SqlExpression, type) {
    const filterFunc = {
      leaf_node: {
        operator: type || 'knowledge_filter',
        func: '',
      },
    };
    item.children.push(filterFunc);
  }

  addFilterExpression(item: SqlExpression) {
    const filterExpression = {
      leaf_node: {
        left: null,
        operator: null,
        right: null,
      },
    };
    item.children.push(filterExpression);
  }

  remove(item: any, positionIndex: number, parentItem?: SqlExpression) {
    if (!parentItem || (parentItem == this.value && this.value.children.length == 1)) {
      return;
    }

    const index = parentItem.children.findIndex((filter: any) => {
      return filter.leaf_node == item || filter.node == item;
    });
    if (index >= 0 && index === positionIndex) {
      parentItem.children = parentItem.children.filter((i, idx) => idx != index);
      return;
    }

    parentItem.children.forEach((filter: any) => filter.node && this.remove(item, positionIndex, filter.node));
  }

  getNormalizeOperators(type: FilterEditor) {
    const operators = this.operators.filter((el) => el.editorTypes.includes(type));
    return operators;
  }

  /**
   * 查找目标字段下拉数据集
   * @param value
   * @returns
   */
  findDestFields(value: string) {
    const destFields = this.destFields.find((el) => el.value === value);
    return destFields?.items || [];
  }
}
