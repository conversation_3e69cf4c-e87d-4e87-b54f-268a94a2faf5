export declare type FilterEditor = 'string' | 'int' | 'datetime' | 'boolean';

export interface SqlExpression {
  operator: string;
  children: Array<CompositeSqlExpression>;
}

export interface CompositeSqlExpression {
  node?: SqlExpression;
  leaf_node?: LeafNode;
}
export interface LeafNode {
  left?: string;
  operator?: string;
  right?: string | number;
  func?: string;
}

export interface OperatorType {
  /**
   * 实际值
   */
  value: string;
  /**
   * 操作名称
   */
  label: string;
  /**
   * 操作类型： unary:一元表达式, binary:二元表达式
   */
  type: 'unary' | 'binary';
  /**
   * 支持的操作类型
   */
  editorTypes: FilterEditor[];
  /**
   * 格式化字符串
   */
  format: string;
}
