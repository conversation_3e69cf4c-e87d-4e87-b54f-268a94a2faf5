@top statement { (Template | StringContent)* }

@local tokens {
    TemplateStart[closedBy=TemplateEnd]{ "#{" }
    @else StringContent
}

@precedence {
    Template
    StringContent
}


    Template {
        TemplateStart InnerString* TemplateEnd
    }


@local tokens {
  // Get strings, but don't let them be terminated by escaped quotes
  // InnerString {   ( ![}] )+ }
  TemplateEnd[openedBy=TemplateStart] { "}" }
  @else InnerString
}

@external propSource highlighting from "./highlight.js"
