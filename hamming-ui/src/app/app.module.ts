import { CommonModule, registerLocaleData } from '@angular/common';
import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { HttpClientModule } from '@angular/common/http';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import zh from '@angular/common/locales/zh';

import { TuiAppService, TuiModule } from '@tui/frame';

import { ApiModule } from './sdk/api.module';
import { BASE_PATH } from './sdk/variables';
import { AppComponent } from './app.component';
import { RoutingModule } from './app-routing.module';

registerLocaleData(zh);

export function setupBasePath(tuiAppService) {
  return tuiAppService.tuiApp.serviceUrl.hamming || '/hamming';
}

@NgModule({
  declarations: [AppComponent],
  imports: [CommonModule, BrowserModule, HttpClientModule, TuiModule, ApiModule, RoutingModule, BrowserAnimationsModule],
  providers: [
    {
      provide: BASE_PATH,
      useFactory: setupBasePath,
      deps: [TuiAppService],
    },
  ],
  bootstrap: [AppComponent],
  exports: [AppComponent],
})
export class AppModule {}
