<t-loading [zindex]="1200" [visible]="loading" [ngStyle]="{ zIndex: loading ? '2' : '-2' }"> </t-loading>

<div class="h-listbox">
  <div class="screen-listmore clearfix clear">
    <div class="form-button">
      <t-title [text]="title" [icon]="'default'"></t-title>
    </div>
    <div class="form-column-tab">
      <form t-form [formGroup]="ruleForm" *ngIf="!loading">
        <t-form-item>
          <t-form-label [tSpan]="2" tFor="title" class="senior-form"><b class="form-required">*</b>过滤规则名称</t-form-label>
          <t-form-control [tSpan]="10">
            <input [disabled]="disabled" formControlName="name" tInputText placeholder="请输入规则名称" type="text" [maxlength]="85" />
            <t-form-explain *ngIf="ruleForm.get('name')?.hasError('maxlength')"> 规则名称长度不能超过85个字符. </t-form-explain>
            <t-form-explain *ngIf="ruleForm.get('name').hasError('pattern')">{{nameAlarmMess}}</t-form-explain>
          </t-form-control>
          <span class="icon-help" value="right" [tTooltip]="nameAlarmMess" tooltipPosition="right"></span>
        </t-form-item>

        <t-form-item>
          <t-form-label [tSpan]="2" class="senior-form"><b class="form-required">*</b>过滤告警类型</t-form-label>
          <t-form-control [tSpan]="10">
            <div class="alarm-type">
              <t-radioButton [disabled]="disabled" name="mode" label="指定" value="SPECIFIC" formControlName="filterType" (onClick)="changeAlarmType()"></t-radioButton>
              <t-radioButton [disabled]="disabled" name="mode" label="任意" value="ANY" formControlName="filterType" (onClick)="changeAlarmType()"></t-radioButton>
            </div>
            <div class="form-item-tips inline-block">
              <span>匹配过滤条件的安全日志将不参与告警归并计算，不会生成指定过滤类型的告警。</span>
            </div>
            <div *ngIf="ruleForm.get('filterType').value == 'SPECIFIC'">
              <t-dropdown
                [disabled]="disabled"
                [options]="eventTypes"
                formControlName="cat2Number"
                placeholder="请选择过滤告警类型"
                appendTo="body"
                [layoutModel]="'group-horizontal'"
                [filter]="true"
                [showClear]="true"
                [style]="{ width: '100%', 'margin-bottom': '12px' }"
              >
                <ng-template let-item tTemplate="group">
                  <div [tTooltip]="item.label">
                    <span class="dropdown-group-title">
                      {{ item.label }}
                    </span>
                    <i class="i-latebox icon-paginato-right"></i>
                  </div>
                </ng-template>
                <ng-template let-item tTemplate="selectedItem">
                  <span style="vertical-align: middle; margin-left: 0.5em">{{ item.cat1 }}/{{ item.cat2 }}</span>
                </ng-template>
              </t-dropdown>
            </div>
          </t-form-control>
        </t-form-item>

        <t-form-item>
          <t-form-label [tSpan]="2" tFor="type" class="senior-form"><b class="form-required">*</b>过滤条件</t-form-label>
          <t-form-control [tSpan]="10">
            <div class="match-mode" *ngIf="!disabled">
              <ul class="mode-list">
                <li class="mode-item cursor" [ngClass]="{ active: mode == 0 }" (click)="changeMode(0)">
                  <span>基础模式</span>
                </li>
                <li class="mode-item cursor active" [ngClass]="{ active: mode == 1 }" (click)="changeMode(1)">
                  <span>高级模式</span>
                </li>
              </ul>
            </div>
            <div formGroupName="filters" style="margin-bottom: 16px; position: relative">
              <!-- 基础模式 -->
              <ng-container *ngIf="mode == 0">
                <div class="flex-line" [class.disabled]="disabled" (click)="editFilter()">
                  <div id="sqlText" class="tui-exp-forminput box cursor" style="width: 100%"> {{ ruleForm.get('filters').get('expression').value | convertToSql : operators : filterMap : knowledges : destFields }}</div>
                  <i class="icon-edit filters-addon" tooltip="编辑筛选条件" tooltipposition="top" *ngIf="!disabled"></i>
                </div>
                <ng-container *ngIf="editCondition">
                  <ng-container *ngTemplateOutlet="filterTemplate"> </ng-container>
                </ng-container>
              </ng-container>

              <!-- 高级模式 -->
              <ng-container *ngIf="mode == 1">
                <div>
                  <textarea [attr.disabled]="disabled == true ? true : null" formControlName="predication" tInputTextarea [rows]="5" placeholder="请输入过滤条件"></textarea>
                </div>
                <div *ngIf="mode == 1" class="exp-wrapper">
                  <div class="exp-tip">
                    示例:<br />
                    src_ip like '***********'<br />
                    src_ip = '***********'<br />
                    src_ip in ('***********')<br />
                    src_ip <> '***********'<br />
                    src_ip is null
                  </div>
                </div>
              </ng-container>
            </div>
          </t-form-control>
        </t-form-item>

        <t-form-item>
          <t-form-label [tSpan]="2" class="senior-form">备注</t-form-label>
          <t-form-control [tSpan]="10">
            <div class="textarea-wrapper">
              <textarea formControlName="description" [attr.disabled]="disabled == true ? true : null" tInputTextarea [rows]="4" placeholder="请输入备注" [maxlength]="255"></textarea>
              <span class="count">{{ ruleForm.get('description').value?.length }}/255</span>
            </div>
          </t-form-control>
        </t-form-item>

        <t-form-item>
          <t-form-label [tSpan]="2" class="senior-form">是否启用</t-form-label>
          <t-form-control [tSpan]="10">
            <t-switch formControlName="status" [disabled]="disabled" [labelMap]="{ checked: '启用', unchecked: '禁用' }"></t-switch>
          </t-form-control>
        </t-form-item>
        <t-form-item>
          <t-form-label [tSpan]="2" class="senior-form"></t-form-label>
          <t-form-control class="form-button">
            <button tButton *ngIf="!disabled" type="button" label="测试" [disabled]="ruleForm.invalid" [btloading]="testing" class="ui-button-primary" (click)="test()"></button>
            <button tButton *ngIf="!disabled" type="button" label="保存" (click)="save()" [btloading]="saving" [disabled]="!isTest || ruleForm.invalid" class="ui-button-primary"></button>
            <button tButton type="button" label="返回" class="ui-button-secondary ui-width ml8" (click)="back()"></button>
          </t-form-control>
        </t-form-item>
      </form>
    </div>
  </div>

  <ng-template #filterTemplate>
    <div class="filter-edit">
      <app-sql-filter
        [fields]="filterMap"
        [invalidFields]="invalidFields"
        [showKnowledge]="showKnowledge"
        [operators]="operators"
        [knowledges]="knowledges"
        [destFields]="destFields"
        [funcRender]="funcTemplate"
        [(value)]="condition"
        (valueChange)="conditionChange($event)"
      >
        <ng-template #funcTemplate let-item>
          <span>{{ item.label }}</span>
        </ng-template>
      </app-sql-filter>
      <div class="filter-edit-footer">
        <button class="ui-button-primary" tButton type="button" label="生成匹配条件" [disabled]="!canSave" (click)="saveCondition()"></button>
        <button class="ui-button-secondary" tButton type="button" label="取消" (click)="closeEdit()"></button>
      </div>
    </div>
  </ng-template>
  <t-toast key="alarm-filter-info"></t-toast>
</div>
