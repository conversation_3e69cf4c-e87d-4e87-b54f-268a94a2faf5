import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { TuiNavService, TuiUserService } from '@tui/frame';
import { MessageService } from '@tui/component-library';

import { MetadataService, AlarmRuleService, AlarmRuleGroupService, KnowledgeService, AlarmFilterService } from 'src/app/sdk';
import { OperatorType, SqlExpression } from 'src/app/components/sql-filter/model';
import { OptionType } from 'src/app/models/option';
import { IsNullorEmpty, checkAuth } from 'src/app/utils';
import { EventBusService } from 'src/app/services/event-bus.service';

@Component({
  selector: 'app-alarm-filter-info',
  templateUrl: './alarm-filter-info.component.html',
  styleUrls: ['./alarm-filter-info.component.less'],
})
export class AlarmFilterInfoComponent implements OnInit {
  destroy$ = new Subject();

  id: string;

  title: string;

  /**
   * 禁用编辑
   */
  disabled = false;

  isTest = false;

  // 加载标识
  loading = false;
  testing = false;
  saving = false;

  /**
   * 显示知识库条件
   */
  showKnowledge = false;

  canSave = false;

  /**
   * 过滤条件禁用字段
   */
  invalidFields = ['cat1', 'cat2'];

  // 告警类型下拉数据
  eventTypes: OptionType[] = [];

  // 过滤条件模式  基础：0 高级： 1
  mode = 0;

  // 字段映射映射树集合
  fieldMapTree: Array<OptionType> = [];

  fieldMapOpts: Array<OptionType> = [];

  // 模型下拉数据集
  modelOpts: OptionType[] = [];

  // 过滤映射
  filterMap: OptionType[] = [];

  // 知识库下拉
  knowledges: OptionType[] = [];

  // 知识库映射字段下拉
  destFields: OptionType[] = [];

  // 保留关键字 匹配时忽略大小写，此处需要小写
  keywords: Array<string> = ['alarmname', 'alarmtypecat1', 'alarmtypecat2', 'alarmlevel', 'alarmconent', 'tags', 'confidencelevel'];

  operators: OperatorType[] = [];

  editCondition = false;

  condition: SqlExpression;
  nameAlarmMess = `只能输入中文、英文、数字和符号(如:。，；：！？（）《》——、.,;:!?"'(){}[]_-&#` + '`';

  // 表单对象
  ruleForm: FormGroup = this.fb.group({
    name: ['', [Validators.required, Validators.pattern(/^[a-zA-Z0-9。，；：！？（）《》——、\.\,\;\:\!\?\'\"\(\)\{\}\[\]\_\-\&\#\`\u4e00-\u9fa5]+$/), Validators.maxLength(85)]],
    description: ['', [Validators.maxLength(255)]],
    filterType: ['SPECIFIC'],
    // 告警类型数据
    cat2Number: ['', [Validators.required]],
    filters: this.fb.group(
      {
        predication: [''],
        expression: [null],
      },
      {
        validators: [this.filtersValidator()],
      },
    ),
    status: [true],
  });

  get fieldMap() {
    return this.ruleForm.get('fieldMap') as FormArray;
  }

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private fb: FormBuilder,
    private msg: MessageService,
    private tuiNavService: TuiNavService,
    private tuiUser: TuiUserService,
    private eventbus: EventBusService,
    private metadataService: MetadataService,
    private alarmRuleService: AlarmRuleService,
    private alarmRuleGroupService: AlarmRuleGroupService,
    private alarmFilterService: AlarmFilterService,
    private knowledgeService: KnowledgeService,
  ) {}

  ngOnInit() {
    setTimeout((el) => {
      this.title = this.tuiNavService.curNav.label || '';
      this.title = this.title.replace(/\[[\s\S]*\]/, '');
    });

    this.activatedRoute.params.pipe(takeUntil(this.destroy$)).subscribe((params) => {
      this.getOptions();

      if (params.id) {
        this.id = params.id;
        this.getFormInfo(params.id);
      }
    });

    // 数据变更重置保存按钮
    this.ruleForm.valueChanges.subscribe((_) => {
      this.isTest = false;
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * 获取下拉数据
   */
  getOptions() {
    // 规范化告警类型下拉数据
    function normalize(data) {
      const result = new Map<string, any>();
      data.forEach((el: any) => {
        const node = {
          label: el.name,
          value: `${el.number}`,
          cat1: el.cat,
          cat2: el.name,
        };

        if (result.has(el.cat)) {
          const parent = result.get(el.cat);
          parent.items.push(node);
        } else {
          result.set(el.cat, {
            label: el.cat,
            value: el.cat,
            items: [node],
          });
        }
      });

      return [...result.values()];
    }

    // 获取告警类型下拉数据
    this.alarmRuleGroupService
      .getAlarmRuleGroups()
      .pipe(takeUntil(this.destroy$))
      .subscribe((data) => {
        // 过滤页面传递过来的cat类型
        this.eventTypes = normalize(data);
      });

    this.getFieldMap('security_log', (res: any[]) => {
      this.filterMap = res;
    });

    /* // 获取关键字数据，字段映射时需要排除
    this.getFieldMap('alarm_indexing', (res) => {
      res.forEach((element) => {
        element.items.forEach((el) => {
          this.keywords.push(el.value);
        });
      });
    });*/

    // 获取sql条件下拉数据
    this.getAlarmRuleExpression();

    // 判断是否有知识库权限
    // if (checkAuth('/hamming/alarm-filter', '/hamming/alarm-filter/knowledge')) {
    this.showKnowledge = true;
    // 获取知识库下拉列表
    this.knowledgeService
      .getKnowledge()
      .pipe(takeUntil(this.destroy$))
      .subscribe((data: OptionType[]) => {
        this.knowledges = data;
      });

    // 获取知识库对应字段下拉列表
    this.knowledgeService
      .getKnowledgeFields()
      .pipe(takeUntil(this.destroy$))
      .subscribe((data: OptionType[]) => {
        this.destFields = data;
      });
    // }
  }

  changeAlarmType() {
    if (this.ruleForm.get('filterType').value !== 'ANY') {
      this.ruleForm.get('cat2Number').setValidators([Validators.required]);
      this.invalidFields = ['cat1', 'cat2'];
    } else {
      this.ruleForm.get('cat2Number').clearValidators();
      this.invalidFields = [];
    }

    // 编辑状态更新条件
    if (this.editCondition) {
      this.condition = this.shuffleValue(this.condition, true);
      this.canSave = this.validate(this.condition);
    }

    // 更新过滤条件
    let expression = this.ruleForm.get('filters').get('expression').value;
    if (expression) {
      expression = this.shuffleValue(expression, true);
      this.ruleForm.get('filters').get('expression').setValue(expression);
    }

    this.ruleForm.markAsDirty();
    this.ruleForm.get('cat2Number').updateValueAndValidity();
  }

  /**
   * 切换基础/高级模式
   * @param mode 模式：0 -基础  1 - 高级
   */
  changeMode(mode: 0 | 1) {
    this.mode = mode;
    this.ruleForm.get('filters').updateValueAndValidity();
  }

  /**
   * 获取告警规则详情
   * @param id id
   */
  getFormInfo(id: string) {
    this.loading = true;
    this.alarmFilterService
      .getAlarmFilterRuleData(id)
      .pipe(takeUntil(this.destroy$))
      .subscribe(
        (res: any) => {
          this.loading = false;

          // 启用状态不能编辑
          this.disabled = res.status != 'DISABLED' || Object.values(this.tuiUser.curUser.tenant || {}).filter((e) => e).length > 0;

          if (res.filters.predication) {
            this.mode = res.filters.predication ? 1 : 0;
          }

          this.ruleForm.patchValue({
            name: res.name,
            description: res.description || '',
            filterType: res.filterType,
            cat2Number: res.cat2Number,
            filters: {
              predication: res.filters.predication,
              expression: res.filters.expression.node,
            },
            status: res.status == 'ENABLED' ? true : false,
          });

          this.changeAlarmType();
        },
        (res) => {
          this.loading = false;
          this.msg.add({
            key: 'alarm-filter-info',
            severity: 'error',
            summary: '提示',
            detail: res.error.message || res.message,
          });
        },
      );
  }

  /**
   * 获取告警字段映射数据集
   * @param type 类型和
   * @param callback 回调
   */
  getFieldMap(type, callback): void {
    this.metadataService
      .getMetadataFields(type)
      .pipe(takeUntil(this.destroy$))
      .subscribe(
        (res) => {
          const data = [];

          res.forEach((el) => {
            const item = {
              label: el.label,
              value: el.label,
              items: el.items.map((el) => ({
                label: `${el.nameCn}(${el.name})`,
                value: el.isKeyword ? '`' + el.name + '`' : el.name,
                type: el.businessType,
                common: el.commonMergeField.modifiable,
                format: el.isKeyword ? '`' + el.name + '`' : el.name,
                description: el.nameCn,
                suggestions: el.fieldValues.map((el) => ({
                  label: el.fieldDicValue,
                  value: el.fieldValue,
                })),
              })),
            };
            data.push(item);
          });

          callback(data);
        },
        (res) => {
          this.msg.add({
            key: 'alarm-filter-info',
            severity: 'error',
            summary: '提示',
            detail: res.error.message || res.message,
          });
        },
      );
  }

  /**
   * 获取筛选条件
   */
  getAlarmRuleExpression() {
    this.alarmRuleService
      .getFunctions('scalar-function')
      .pipe(takeUntil(this.destroy$))
      .subscribe(
        (res: any) => {
          this.operators = res.map((data) => ({
            label: data.nameCn,
            value: data.name,
            editorTypes: data.paramterTypes[0].split(','),
            format: data.format,
            type: data.paramtersNumber == 1 ? 'unary' : 'binary',
          }));
        },
        (res) => {
          this.msg.add({
            key: 'alarm-filter-info',
            severity: 'error',
            summary: '提示',
            detail: res.error.message || res.message,
          });
        },
      );
  }

  /**
   *编辑匹配条件
   */
  editFilter() {
    if (this.editCondition) {
      return;
    }
    this.editCondition = true;

    const condition = this.ruleForm.get('filters').get('expression').value;
    if (!condition) {
      this.condition = {
        operator: 'and',
        children: [
          {
            leaf_node: {
              left: null,
              operator: null,
              right: null,
            },
          },
        ],
      };
    } else {
      this.condition = JSON.parse(JSON.stringify(condition));
    }
    this.canSave = this.validate(this.condition);
  }

  /**
   * 删除过滤条件
   * @param index 索引
   */
  deleteItem(index: number): void {
    this.fieldMap.removeAt(index);
  }

  /**
   * 关键字验证
   * @returns 验证方法
   */
  keywordsValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const forbidden = (this.keywords || []).includes((control.value || '').toLowerCase());
      return forbidden ? { keywords: true } : null;
    };
  }

  /**
   * 验证字段重复
   * @returns ValidatorFn
   */
  repeatFieldMapValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (this.ruleForm) {
        const values = this.fieldMap.getRawValue();
        const filter = values.filter((val) => val.name == control.value);
        return filter.length > 1 ? { repeat: true } : null;
      } else {
        return null;
      }
    };
  }

  /**
   * 验证匹配信息
   * @returns ValidatorFn
   */
  filtersValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (this.ruleForm) {
        if (this.mode == 0) {
          const value = this.ruleForm.get('filters').get('expression').value;

          if (value == null || !this.validate(value)) {
            return { required: true };
          }
          return null;
        } else {
          const value = this.ruleForm.get('filters').get('predication').value;
          return !value || value.trim() == '' ? { required: true } : null;
        }
      } else {
        return null;
      }
    };
  }

  /**
   * 测试
   */
  test() {
    this.testing = true;
    const model = this.normalizeRawData();
    model.id = this.id;

    this.alarmFilterService
      .testAlarmFilterRule(model)
      .pipe(takeUntil(this.destroy$))
      .subscribe(
        (res) => {
          this.isTest = true;
          this.testing = false;
          this.msg.add({
            key: 'alarm-filter-info',
            severity: 'success',
            summary: '提示',
            detail: res.message,
          });
        },
        (res) => {
          this.testing = false;
          this.msg.add({
            key: 'alarm-filter-info',
            severity: 'error',
            summary: '提示',
            detail: res.error.message || res.message,
          });
        },
      );
  }

  /**
   * 保存
   */
  save(): void {
    this.saving = true;
    const model = this.normalizeRawData();
    model['isTested'] = true;
    // 没有id 为新增，否则修改
    if (!this.id) {
      this.alarmFilterService
        .saveAlarmFilterRule(model)
        .pipe(takeUntil(this.destroy$))
        .subscribe(
          (res) => {
            this.saving = false;
            this.back();
            this.eventbus.trigger('alarm-filter');
          },
          (res) => {
            this.saving = false;
            this.msg.add({
              key: 'alarm-filter-info',
              severity: 'error',
              summary: '提示',
              detail: res.error.message || res.message,
            });
          },
        );
    } else {
      this.alarmFilterService
        .editAlarmFilterRule(this.id, model)
        .pipe(takeUntil(this.destroy$))
        .subscribe(
          (res) => {
            this.saving = false;
            this.back();
            this.eventbus.trigger('alarm-filter');
          },
          (res) => {
            this.saving = false;
            this.msg.add({
              key: 'alarm-filter-info',
              severity: 'error',
              summary: '提示',
              detail: res.error.message || res.message,
            });
          },
        );
    }
  }

  /**
   * 序列化表单数据
   */
  normalizeRawData() {
    const {
      status,
      filters: { predication, expression },
      ...model
    } = this.ruleForm.getRawValue();

    model.status = status ? 'ENABLED' : 'DISABLED';

    if (model.filterType == 'ANY') {
      model.cat2Number = '';
    }

    // 组装匹配信息
    if (this.mode) {
      model.filters = {
        predication,
      };
    } else {
      model.filters = {
        expression: {
          node: expression,
        },
      };
      model.filtersView = document.querySelector('#sqlText')['innerText'];
    }

    return model;
  }

  /**
   * 返回上一页
   */
  back() {
    this.tuiNavService.closeCurrNav(this.router.url.split('?')[0], false);
    this.router.navigateByUrl('/hamming/alarm-filter');
  }

  conditionChange(filter) {
    this.canSave = this.validate(filter);
  }

  saveCondition() {
    // 校验规则
    this.ruleForm.get('filters').get('expression').setValue(this.condition);
    this.editCondition = false;
  }

  closeEdit() {
    this.editCondition = false;
  }

  validate(filter) {
    if (!filter || !filter.children.length) {
      return false;
    }

    let result = true;
    const filters = filter.children;
    const opType = {};
    this.operators.forEach((operator) => {
      opType[operator.value] = operator.type;
    });

    for (let index = 0; index < filters.length; index++) {
      const item = filters[index];
      const { node, leaf_node } = item;

      if (node) {
        result = this.validate(node);
        if (!result) {
          break;
        }
      } else {
        const { left, operator, right, func } = leaf_node;
        if (func != null) {
          if (func == '') {
            result = false;
            break;
          }
        }

        if (!left || !operator || (func && IsNullorEmpty(right)) || (opType[operator] == 'binary' && IsNullorEmpty(right))) {
          result = false;
          break;
        }
      }
    }

    return result;
  }

  /**
   * 清洗过滤条件数据
   */
  shuffleValue(treeNode, root = false) {
    const node = {
      children: [],
      operator: treeNode.operator,
    };

    treeNode.children.forEach((item) => {
      const obj: any = {};
      Object.keys(item).forEach((key) => {
        if (key == 'node') {
          //
          const node = item[key] ? this.shuffleValue(item[key]) : null;
          if (node && node.children.length) {
            obj.node = node;
          }
        } else if (key == 'leaf_node') {
          if (item[key] && !this.invalidFields.includes(item[key].left)) {
            obj.leaf_node = item[key];
          }
        }
      });
      if (Object.keys(obj).length) {
        node.children.push(obj);
      } else if (root) {
        node.children.push({
          leaf_node: {
            left: '',
            operator: '',
            right: '',
          },
        });
      }
    });

    return node;
  }
}
