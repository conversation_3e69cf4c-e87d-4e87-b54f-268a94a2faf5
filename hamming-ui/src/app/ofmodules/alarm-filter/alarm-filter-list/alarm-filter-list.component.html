<!--应用操作栏-->
<div class="handle-bar">
  <t-handle-bar [title]="title" (onHandleChange)="onHandleChange($event)" [handleSett]="handleSett"
    tooltipPosition="left"> </t-handle-bar>
</div>
<div class="container-box">
  <!--  列表查询条件编辑部分-->
  <div class="searchBox-box" *ngIf="showSearh">
    <div class="searchBox">
      <div class="search-box">
        <span class="search-label">过滤规则名称</span>
        <div class="search-inner">
          <input type="text" [(ngModel)]="searchParams.name" placeholder="请输入" [maxlength]="85" tInputText />
        </div>
      </div>
      <div class="search-box">
        <span class="search-label">过滤告警类型</span>
        <div class="search-inner">
          <t-dropdown [options]="eventTypes" [(ngModel)]="searchParams.cat2Number" placeholder="请选择告警类型" appendTo="body"
            [layoutModel]="'group-horizontal'" [filter]="true" [showClear]="true" [style]="{ width: '100%' }">
            <ng-template let-item tTemplate="group">
              <div [tTooltip]="item.label">
                <span class="dropdown-group-title">
                  {{ item.label }}
                </span>
                <i class="i-latebox icon-paginato-right"></i>
              </div>
            </ng-template>
            <ng-template let-car tTemplate="item">
              <div class="ui-helper-clearfix" style="position: relative; height: 25px">
                <div style="font-size: 14px; float: right; margin-top: 4px">
                  {{ car.label }}
                </div>
              </div>
            </ng-template>
          </t-dropdown>
        </div>
      </div>

      <div class="search-box">
        <span class="search-label">状态</span>
        <div class="search-inner">
          <t-dropdown [options]="statusType" [(ngModel)]="searchParams.status" placeholder="不限" [showClear]="true"
            [style]="{ width: '100%' }" [panelStyle]="{width: '100px'}"></t-dropdown>
        </div>
      </div>
      <div class="search-operation">
        <button tButton label="查询" icon="icon-search2" class="ui-button-primary" (click)="search()"></button>
        <button tButton icon="icon-loop" class="ui-button-color-grey ml8" tTooltip="重置" (click)="reset()"></button>
      </div>
    </div>
  </div>

  <div class="dataview-box clearfix clear flex-1">
    <div class="tableview-bigbox">
      <!--表格列表-->
      <div class="tableview-box">
        <!--表格操作栏-->
        <div class="table-operate">
          <!--操作左侧-->
          <div class="table-operatebox">
            <button aas [uri]="PAGE_URI" [act]="ACTIONS.ADD" tButton type="button" icon="icon-plus" [disabled]="isTenant"
              class="ui-button-pius" tButton tTooltip="添加告警过滤规则" (click)="addAlarmFilterRule()"></button>
              <ng-container *ngIf="!isTenant">
                <div class="table-toggle" *ngIf="!selectedList?.length else otherOp">
                  <button class="ui-button-tool" aas [uri]="PAGE_URI" [act]="ACTIONS.DELETE" tButton label="删除"
                    icon="icon-delete" title="页面规则删除" (click)="delRule(dataList)"></button>
                  <button class="ui-button-tool" aas [uri]="PAGE_URI" [act]="ACTIONS.STATUS" tButton label="启用"
                    icon="icon-play3" title="页面规则启用" (click)="changeStatus(true,dataList)"></button>
                  <button class="ui-button-tool" aas [uri]="PAGE_URI" [act]="ACTIONS.STATUS" tButton label="禁用"
                    icon="icon-stop2" title="页面规则禁用" (click)="changeStatus(false,dataList)"></button>
                </div>
              </ng-container>
            <ng-template #otherOp>
              <div class="table-toggle">
                <button class="ui-button-tool" aas [uri]="PAGE_URI" [act]="ACTIONS.DELETE" tButton label="删除"
                  icon="icon-delete" title="选中规则删除" (click)="delRule(selectedList)"></button>
                <button class="ui-button-tool" aas [uri]="PAGE_URI" [act]="ACTIONS.STATUS" tButton label="启用"
                  icon="icon-play3" title="选中规则启用" (click)="changeStatus(true,selectedList)"></button>
                <button class="ui-button-tool" aas [uri]="PAGE_URI" [act]="ACTIONS.STATUS" tButton label="禁用"
                  icon="icon-stop2" title="选中规则禁用" (click)="changeStatus(false,selectedList)"></button>
              </div>
            </ng-template>
          </div>
          <!--操作右侧-->
          <div class="table-operright" *ngIf="!isTenant">
            <div class="table-toggle">
              <button tButton class="ui-button-tool" aas [uri]="PAGE_URI" [act]="ACTIONS.IMPORT" icon="icon-import1"
                label="导入" (click)="import()"></button>
              <button tButton class="ui-button-tool" aas [uri]="PAGE_URI" [act]="ACTIONS.EXPORT" icon="icon-export1"
                label="导出" [disabled]="!dataList.length" (click)="export()"></button>
            </div>
          </div>
        </div>
        <!--表格数据列表-->
        <div class="box-table">
          <div class="table-content">
            <t-table [columns]="columns" [data]="dataList" [(selection)]="selectedList" [scrollable]="true"
              [autoLayout]="true" [resizableColumns]="true" [reorderableColumns]="true">
              <ng-template tTemplate="colgroup" let-columns>
                <colgroup>
                  <col width="25" />
                  <col width="50" />
                  <ng-container *ngFor="let col of columns">
                    <col [width]="col.width" />
                  </ng-container>
                </colgroup>
              </ng-template>
              <ng-template tTemplate="header" let-columns>
                <tr>
                  <th>
                    <t-tableHeaderCheckbox></t-tableHeaderCheckbox>
                    <!-- <t-checkbox [binary]="true" [(ngModel)]="allSelected" (onChange)="setSelectedBoole(allSelected)"></t-checkbox> -->
                  </th>
                  <th>序号</th>
                  <ng-container *ngFor="let col of columns">
                    <th tResizableColumn tFrozenColumn [frozen]="col.frozen" [alignFrozen]="col.align">
                      <div class="flex" (click)="sortTable(col)">
                        <span>{{ col.header }}</span>
                        <span class="flex cursor" *ngIf="col.selected">
                          <i class="icon-tabicon" *ngIf="col.sortType === ''"></i>
                          <i class="icon-tabup" *ngIf="col.sortType === 'ASC'"></i>
                          <i class="icon-tabdown" *ngIf="col.sortType === 'DESC'"></i>
                        </span>
                      </div>
                    </th>
                  </ng-container>
                </tr>
              </ng-template>
              <ng-template tTemplate="body" let-model let-columns="columns" let-index="rowIndex">
                <tr [tReorderableRow]="index">
                  <td>
                    <t-tableCheckbox [value]="model"></t-tableCheckbox>
                  </td>
                  <td>{{ (pageNo - 1) * pageSize + index + 1 }}</td>
                  <td>
                    <a class="cursor text-overflow" *ngIf="!model.status" tTooltip="{{ model.name }}"
                      tooltipPosition="bottom" aas [uri]="PAGE_URI" [act]="ACTIONS.EDIT" dpt="disable"
                      (click)="toEdit(model.id)">{{ model.name }}</a>
                    <a class="cursor text-overflow" *ngIf="model.status" tTooltip="{{ model.name }}"
                      tooltipPosition="bottom" aas [uri]="PAGE_URI" [act]="ACTIONS.VIEW" dpt="disable"
                      (click)="toView(model.id)">{{ model.name }}</a>
                  </td>
                  <td>
                    <div class="text-overflow" tTooltip="{{ model.cat2 }}" tooltipPosition="bottom"> {{ model.cat2 }}
                    </div>
                  </td>
                  <td>
                    <ng-container *ngIf="model.filters.predication !== null">
                      <div class="text-overflow" tTooltip="{{ model.filters.predication }}" tooltipPosition="bottom">
                        {{ model.filters.predication }}
                      </div>
                    </ng-container>
                    <div class="text-overflow" *ngIf="model.filters.predication == null"
                      tTooltip="{{ model.filters.expression.node | convertToSql : operators : filterMap : knowledges : destFields :model.filtersView }}"
                      tooltipPosition="bottom">
                      {{ model.filters.expression.node | convertToSql : operators : filterMap : knowledges : destFields:
                      model.filtersView }}
                    </div>
                  </td>
                  <td>
                    <t-switch [(ngModel)]="model.status" aas [uri]="PAGE_URI" [act]="ACTIONS.STATUS" dpt="disable"
                      [labelMap]="{ checked: '启用', unchecked: '禁用' }" [isLoading]="model.isLoading"
                      (change)="changeStatus($event, model)"></t-switch>
                  </td>
                  <td>
                    <div class="text-overflow" tTooltip="{{ model.description }}" tooltipPosition="bottom"> {{
                      model.description }}</div>
                  </td>
                  <!-- <td class="text-overflow">
                    <span>{{ model.creator }}</span>
                  </td> -->
                  <td>
                    <div class="text-overflow" tTooltip="{{ model.updater }}" tooltipPosition="bottom"> {{ model.updater
                      }}</div>
                  </td>
                  <!--                  <td class="text-overflow">-->
                  <!--                    <span>{{ model.triggerTime }}</span>-->
                  <!--                  </td>-->
                  <!-- <td class="text-overflow">
                    <span>{{ model.createTime }}</span>
                  </td> -->
                  <td>
                    <div class="text-overflow" tTooltip="{{ model.lastUpdatedTime }}" tooltipPosition="bottom"> {{
                      model.lastUpdatedTime }}</div>
                  </td>
                  <td tFrozenColumn [frozen]="true" [alignFrozen]="'right'">
                    <!-- <a class="link cursor" *ngIf="!model.status || model.source != 'BUILT_IN'" (click)="toEdit(model.id)"><i class="icon-edit" tTooltip="编辑" tooltipPosition="bottom"></i></a> -->
                    <a class="link cursor" *ngIf="!model.status" aas [uri]="PAGE_URI" [act]="ACTIONS.EDIT"
                      (click)="toEdit(model.id)"><i class="icon-edit" tTooltip="编辑" tooltipPosition="bottom"></i></a>
                    <a class="link cursor" *ngIf="model.status" aas [uri]="PAGE_URI" [act]="ACTIONS.VIEW"
                      (click)="toView(model.id)"><i class="icon-eye1" tTooltip="查看" tooltipPosition="bottom"></i></a>

                    <!-- <a class="link cursor" (click)="viewAlram(model.id)"
                ><i
                  class="icon-eye1"
                  tTooltip="查看安全告警"
                  tooltipPosition="bottom"
                  style="font-size: 14px"
                ></i
              ></a> -->
                    <a class="cursor" aas [uri]="PAGE_URI" [act]="ACTIONS.DELETE" [ngClass]="{ disabled: model.status }"
                      (click)="delRule(model.id)">
                      <i class="icon-delete3" tTooltip="删除" tooltipPosition="bottom"></i>
                    </a>
                  </td>
                </tr>
              </ng-template>
            </t-table>
          </div>
        </div>

        <div class="tui-exp-footer flex-0">
          <p class="num-con">
            第<span class="primary-text">{{ pageNo }}/{{ pageNums }}</span>页&nbsp;&nbsp; 共<span class="primary-text">{{
              totals }}</span>条记录
          </p>
          <t-paginator [rows]="pageSize" [totalRecords]="totals" (onPageChange)="onPageChange($event)"
            [rowsPerPageOptions]="[20, 30, 50]" [jumpToPage]="true" #paginator></t-paginator>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 批量导入的对话框 -->
<t-dialog class="bjj-con" header="导入告警过滤规则" [(visible)]="importRules" [modal]="true" [responsive]="true"
  [style]="{ width: '800px' }" [minY]="10" (onShow)="initDialog()" (onHide)="closeUpload()">
  <div class="upload">
    <t-button icon="iconfont icon-plus5" label="选择文件"></t-button>
    <t-fileUpload #uploadDom class="upload-file" styleClass="modelsys-upload" mode="basic" chooseLabel="选择文件"
      name="file" method="POST" [url]="importUrl" [maxFileSize]="10485760"
      accept="application/x-zip-compressed,application/x-zip,application/zip" invalidFileSizeMessageDetail="最大上传大小为10M"
      invalidFileTypeMessageDetail="只允许上传zip格式文件" (onSelect)="selectedFile($event)" (onUpload)="successFile($event)"
      (onBeforeUpload)="beginUpload()" (onError)="uploadError($event)">
    </t-fileUpload>
    <p class="upload-text" [ngStyle]="{ color: uploadData.disabled ? '#bfbfbf' : '#333' }">
      {{ uploadData.text }}
      <i *ngIf="!uploadData.disabled" class="icon-delete3" (click)="clearFile()"></i>
    </p>
    <t-button *ngIf="!uploadData.disabled" icon="iconfont icon-upload1" label="上传" (onClick)="uploadFile()"> </t-button>
  </div>
  <div style="height: 30px; margin: 10px 0" *ngIf="uploadData.progress">
    <p>正在上传解析中..........</p>
    <t-progressBar mode="indeterminate" [style]="{ height: '6px' }"></t-progressBar>
  </div>
  <div style="display: none" [ngStyle]="{
      display: uploadData.information && !uploadData.progress ? 'block' : 'none'
    }">
    <div class="upload-tip">
      {{uploadData.information?.summary}}
    </div>
    <div class="upload-table"
      *ngIf="uploadData.information?.ruleContent && uploadData.information?.ruleContent.length > 0">
      <t-table [data]="uploadData.information?.ruleContent" [scrollable]="true" [style]="{ width: '100%' }"
        [scrollHeight]="uploadData.tableHeightToScroll">
        <ng-template tTemplate="colgroup" let-columns>
          <colgroup>
            <col [style.width]="'150px'" />
            <col [style.width]="'150px'" />
          </colgroup>
        </ng-template>
        <ng-template tTemplate="header">
          <tr>
            <th>过滤规则名称</th>
            <th>原因</th>
          </tr>
        </ng-template>
        <ng-template tTemplate="body" let-list>
          <tr>
            <td [tooltipZIndex]="'10000000'" tTooltip="{{ list.name }}" tooltipPosition="bottom" style="width: 150px">
              {{ list.name }}
            </td>
            <td class="text-overflow" [tooltipZIndex]="'10000000'" tTooltip="{{ list.errorType }}"
              tooltipPosition="bottom" style="width: 150px">
              {{ list.errorType }}
            </td>
          </tr>
        </ng-template>
      </t-table>
    </div>
    <div class="upload-actions">
      <button tButton type="button" label="关闭" class="ui-width ui-button-primary" style="margin-right: 20px"
        (click)="closeUpload()"></button>
    </div>
  </div>
</t-dialog>

<t-toast key="alarm-filter"></t-toast>
<t-confirmDialog key="alarm-rule-filter"></t-confirmDialog>