import { ChangeDetectorRef, Component, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';

import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { TuiNavService, TuiUserService } from '@tui/frame';
import { ConfirmationService, MessageService } from '@tui/component-library';
import { PaginatorComponent } from '@tui/component-library/src/app/ngtui/components/paginator/paginator/paginator.component';

import { AlarmRuleView, AlarmRuleService, AlarmRuleGroupService, AlarmFilterService, MetadataService, KnowledgeService } from 'src/app/sdk';
import { OperatorType } from 'src/app/components/sql-filter/model';
import { OptionList, OptionType } from 'src/app/models/option';
import { Routingname } from 'src/app/models/route';
import { StatusType } from 'src/app/models/status';
import { EventBusService } from 'src/app/services/event-bus.service';
import { exportRule } from 'src/app/utils';

@Component({
  selector: 'app-alarm-filter-list',
  templateUrl: './alarm-filter-list.component.html',
  styleUrls: ['./alarm-filter-list.component.less'],
})
export class AlarmFilterListComponent implements OnInit {
  @ViewChild('uploadDom') uploadDom;
  @ViewChild('paginator') paginator: PaginatorComponent;

  destroy$ = new Subject();

  title: string;
  // 是否是租户用户
  isTenant = true;
  /**
   * 页面事件总线监听
   */
  onMounted: () => void;

  /**
   * aas权限操作唯一标识
   */
  PAGE_URI = '/hamming/alarm-filter';

  ACTIONS = {
    ADD: '/hamming/alarm-filter/add',
    EDIT: '/hamming/alarm-filter/edit/*',
    VIEW: '/hamming/alarm-filter/view/*',
    DELETE: '/hamming/alarm-filter/delete',
    STATUS: '/hamming/alarm-filter/status',
    IMPORT: '/hamming/alarm-filter/import',
    EXPORT: '/hamming/alarm-filter/export',
  };

  // 加载标识
  loading = false;
  // 分页条数
  pageSize = 20;
  // 当前页
  pageNo = 1;
  // 数据总数
  totals = 0;

  // 告警类型下拉数据
  eventTypes: OptionList = [];

  // 状态下拉数据
  statusType: OptionList = StatusType;

  // 已选择列集合
  selectedList: Array<AlarmRuleView> = [];

  // 表格数据集
  dataList: Array<AlarmRuleView> = [];

  // 过滤映射
  filterMap: OptionType[] = [];

  // 知识库下拉
  knowledges: OptionType[] = [];

  // 知识库映射字段下拉
  destFields: OptionType[] = [];

  operators: OperatorType[] = [];

  // 上传url
  importUrl = '';

  // 上传的文件
  uploadedFiles: any[] = [];

  // 导入弹窗显示标识
  importRules = false;

  //按钮与日期

  handleSett: any = {
    actionButton: [
      {
        show: true,
        selected: true,
        label: '自定义搜索',
        icon: 'icon-search-3',
        open: false,
      },
    ],
    calendar: { show: false, selected: null, dateFormat: '', absoluteTimeRang: false },
  };

  // 显示搜索区域
  showSearh = true;

  // 查询条件
  searchParams = {
    // 告警类型
    cat2Number: '',
    status: '',
    name: '',
    sort: 'lastUpdatedTime',
    sortType: 'DESC',
  };

  // 表单上方按钮组
  options = [
    { icon: 'icon-plus5', lable: '编辑', value: 'edit', tooltip: '编辑' },
    {
      icon: 'icon-editorial-team',
      lable: '删除',
      value: 'delete',
      tooltip: '删除',
    },
    { icon: 'icon-delete3', lable: '启用', value: 'delete3', tooltip: '启用' },
    { icon: 'icon-release', lable: '禁用', value: 'release', tooltip: '禁用' },
  ];

  // 表格列
  columns = [
    {
      header: '过滤规则名称',
      field: 'name',
      width: 200,
      sortType: '',
      selected: false,
    },
    {
      header: '过滤告警类型',
      field: 'cat',
      sortType: '',
      width: 110,
      selected: false,
    },
    {
      header: '过滤条件',
      field: 'filtersView',
      sortType: '',
      width: 450,
      selected: false,
    },
    {
      header: '状态',
      field: 'status',
      sortType: '',
      width: 80,
      selected: true,
    },
    {
      header: '备注',
      field: 'description',
      sortType: '',
      width: 150,
      selected: false,
    },
    // {
    //   header: '创建人',
    //   field: 'creator',
    //   sortType: '',
    //   width: 120,
    //   frozen: false,
    //   selected: true,
    // },
    {
      header: '修改人',
      field: 'updater',
      sortType: '',
      width: 120,
      frozen: false,
      selected: true,
    },
    // {
    //   header: '触发时间',
    //   field: 'triggerTime',
    //   sortType: '',
    //   width: 150,
    //   frozen: false,
    //   selected: true,
    // },
    // {
    //   header: '创建时间',
    //   field: 'createTime',
    //   sortType: '',
    //   width: 150,
    //   frozen: false,
    //   selected: true,
    // },
    {
      header: '修改时间',
      field: 'lastUpdatedTime',
      sortType: '',
      width: 100,
      frozen: false,
      selected: true,
    },
    { header: '操作', width: 150, frozen: true, align: 'right' },
  ];

  // 上传操作对象
  uploadData = {
    disabled: true,
    file: null,
    text: '请选择文件',
    progress: false,
    information: null,
    maxHeight: 300,
    tableHeightToScroll: '50px',
    timer: 0,
  };

  uploadErrorType = {
    NAME_CONFLICT: '名称冲突',
    ID_CONFLICT: '编号冲突',
    FORMAT_ERROR: '格式错误',
    ILLEGAL_PARAMETER: '参数不合法',
  };

  /**
   * 是否可以批量启用
   */
  get canEnable(): boolean {
    const arr = this.selectedList.length == 0 ? this.dataList : this.selectedList;
    return arr.every((el: any) => el.status);
  }

  /**
   * 是否可以排序
   */
  get canReorder(): boolean {
    return this.dataList.some((el) => el.seq != -1);
  }

  /**
   * 是否可以批量停用
   */
  get canDisabled(): boolean {
    const arr = this.selectedList.length == 0 ? this.dataList : this.selectedList;
    return arr.every((el: any) => !el.status);
  }

  /**
   * 是否可以批量删除
   */
  get canDelete(): boolean {
    const arr = this.selectedList.length == 0 ? this.dataList : this.selectedList;
    return arr.every((el: any) => el.status);
  }

  /**
   * 获取页数
   */
  get pageNums() {
    return Math.ceil(this.totals / this.pageSize) || 1;
  }

  constructor(
    private router: Router,
    private activatedRouter: ActivatedRoute,
    private ref: ChangeDetectorRef,
    public tuiNavSer: TuiNavService,
    private tuiUser: TuiUserService,
    private eventbus: EventBusService,
    private msg: MessageService,
    private metadataService: MetadataService,
    private knowledgeService: KnowledgeService,
    private confirmationService: ConfirmationService,
    private alarmRuleService: AlarmRuleService,
    private alarmRuleGroupService: AlarmRuleGroupService,
    private alarmFilterService: AlarmFilterService,
  ) {
    this.importUrl = `/${Routingname.R}/alarm-filter/import?principal=${this.tuiUser.curUser.principal}`;
  }

  ngOnInit(): void {
    this.isTenant = Object.values(this.tuiUser.curUser.tenant || {}).filter((e) => e).length > 0;
    setTimeout((el) => {
      this.title = this.tuiNavSer.curNav.label;
    });

    this.activatedRouter.queryParams.subscribe((params) => {
      if (params.name && params.name != '') {
        this.searchParams.name = params.name;
      }

      if (params.type && params.type != '') {
        this.searchParams.cat2Number = params.type;
      }

      this.fetchData();
    });

    // 获取告警级别和置信度下拉数据
    this.getOptions();

    // 添加页面刷新回调监听
    this.onMounted = () => {
      this.fetchData();
    };

    this.eventbus.bind('alarm-filter', this.onMounted);
  }

  ngOnChanges(changes: SimpleChanges) {}

  ngOnDestroy(): void {
    this.eventbus.unbind('alarm-filter', this.onMounted);
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * 获取表格数据
   */
  fetchData(): void {
    const { cat2Number, status, name, sort, sortType } = this.searchParams;

    let filterType = '';
    if (cat2Number == 'ANY') {
      filterType = 'ANY';
    } else if (cat2Number != '' && cat2Number != null) {
      filterType = 'SPECIFIC';
    }

    this.selectedList = [];
    this.loading = true;
    this.alarmFilterService
      .getAlarmFilterRules(this.pageNo, this.pageSize, name.trim(), filterType, cat2Number !== 'ANY' ? cat2Number : '', status, sort, sortType)
      .pipe(takeUntil(this.destroy$))
      .subscribe(
        (res: any) => {
          this.loading = false;
          this.pageNo = res.pageable.page;
          this.totals = res.pageable.total;
          this.dataList = res.data.map((el) => ({
            ...el,
            status: el.status == 'ENABLED' ? true : false,
          }));

          this.ref.markForCheck();
        },
        (res) => {
          this.loading = false;
          this.ref.markForCheck();
          this.msg.add({
            key: 'alarm-filter',
            severity: 'error',
            summary: '提示',
            detail: res.error.message || res.message,
          });
        },
      );
  }

  getOptions() {
    function normalize(data) {
      const result = new Map<string, any>();
      data.forEach((el: any) => {
        const node = {
          label: el.name,
          value: `${el.number}`,
          cat1: el.cat,
          cat2: el.name,
        };

        if (result.has(el.cat)) {
          const parent = result.get(el.cat);
          parent.items.push(node);
        } else {
          result.set(el.cat, {
            label: el.cat,
            value: el.cat,
            items: [node],
          });
        }
      });

      return [...result.values()];
    }
    // 获取告警类型下拉数据
    this.alarmRuleGroupService
      .getAlarmRuleGroups()
      .pipe(takeUntil(this.destroy$))
      .subscribe((data) => {
        this.eventTypes = normalize(data);
        this.eventTypes.unshift({
          label: '任意',
          value: '任意',
          items: [
            {
              label: '任意',
              value: 'ANY',
            },
          ],
        });
      });

    this.alarmRuleService
      .getFunctions('scalar-function')
      .pipe(takeUntil(this.destroy$))
      .subscribe(
        (res: any) => {
          this.operators = res.map((data) => ({
            label: data.nameCn,
            value: data.name,
            editorTypes: data.paramterTypes[0].split(','),
            format: data.format,
            type: data.paramtersNumber == 1 ? 'unary' : 'binary',
          }));
        },
        (res) => {
          this.msg.add({
            key: 'alarm-filter',
            severity: 'error',
            summary: '提示',
            detail: res.error.message || res.message,
          });
        },
      );

    this.metadataService.getMetadataFields('security_log').subscribe((res: any[]) => {
      this.filterMap = [];
      res.forEach((el) => {
        const item = {
          label: el.label,
          value: el.label,
          items: el.items.map((el) => {
            el.commonMergeField = el.commonMergeField || el.CommonMergeField;
            const item = {
              common: el.commonMergeField.show,
              label: el.nameCn,
              value: el.isKeyword ? '`' + el.name + '`' : el.name,
              format: el.isKeyword ? '`' + el.name + '`' : el.name,
              description: el.nameCn,
              type: el.businessType,
            };
            return item;
          }),
        };
        this.filterMap.push(item);
      });
    });

    // 获取知识库下拉列表
    this.knowledgeService
      .getKnowledge()
      .pipe(takeUntil(this.destroy$))
      .subscribe((data: OptionType[]) => {
        this.knowledges = data;
      });

    // 获取知识库对应字段下拉列表
    this.knowledgeService
      .getKnowledgeFields()
      .pipe(takeUntil(this.destroy$))
      .subscribe((data: OptionType[]) => {
        this.destFields = data;
      });
  }

  setSelectedBoole(v) {
    this.selectedList = [];
    if (v) {
      this.dataList.forEach((item: any) => {
        item.selected = true;
        this.selectedList.push(item);
      });
    } else {
      this.dataList.forEach((item: any) => {
        item.selected = false;
      });
      this.selectedList = [];
    }
  }

  /**
   * 搜索
   */
  search(): void {
    this.pageNo = 1;
    this.paginator.pageIndex = 0;
    this.fetchData();
  }

  /**
   * 重置
   */
  reset(): void {
    for (const key in this.columns) {
      this.columns[key].sortType = '';
    }

    // 查询条件
    this.searchParams = {
      ...this.searchParams,
      status: '',
      cat2Number: '',
      name: '',
      sort: 'lastUpdatedTime',
      sortType: 'DESC',
    };

    // 分页条件
    this.pageSize = 20;
    this.pageNo = 1;
    this.paginator.pageIndex = 0;

    this.fetchData();
  }

  /**
   * 快捷区域点击事件
   * @param e event
   */
  onHandleChange(e) {
    for (const action of e.actionButton) {
      if ('自定义搜索' == action.label) {
        this.showSearh = action.selected;
      }
    }
  }

  /**
   * 表单排序
   * @param item
   */
  sortTable(item: { sortType: string; field: string; selected: boolean }) {
    if (!item.selected) {
      return;
    }
    const ar = ['', 'ASC', 'DESC'],
      sortType = ar[(ar.indexOf(item.sortType) + 1) % 3];
    this.columns.forEach((e) => (e.sortType = item.field !== e.field ? '' : sortType));
    this.searchParams.sort = item.sortType === '' ? 'lastUpdatedTime' : item.field;
    this.searchParams.sortType = item.sortType === '' ? 'DESC' : item.sortType;

    this.search();
  }

  /**
   * 添加规则
   */
  addAlarmFilterRule(): void {
    const url = `./add`;

    this.router.navigate([url], {
      relativeTo: this.activatedRouter,
    });
  }

  /**
   * 编辑规则
   * @param id id
   */
  toEdit(id: string) {
    const url = `./edit/${id}`;
    this.router.navigate([url], {
      relativeTo: this.activatedRouter,
    });
  }

  /**
   * 查看规则
   * @param id id
   */
  toView(id: string) {
    const url = `./view/${id}`;
    this.router.navigate([url], {
      relativeTo: this.activatedRouter,
    });
  }

  /**
   * 分页改变
   * @param $event event
   */
  onPageChange($event): void {
    this.pageNo = $event.page + 1;
    this.pageSize = $event.rows;

    this.fetchData();
  }

  /**
   * 规则状态变更
   * @param status 状态
   * @param model model
   */
  changeStatus(bln: boolean, model?: any): void {
    const isArr = model instanceof Array;
    const ids = !isArr
      ? model.id
      : model
          .filter((el: any) => el.status != bln)
          .map((el) => el.id)
          .toString();
    const status = bln ? 'ENABLED' : 'DISABLED';
    if (!isArr || this.getOperatStatus(status)) {
      this.loading = true;
      this.alarmFilterService.editAlarmFilterRuleStatus(ids, status).subscribe(
        (res) => {
          this.loading = false;
          this.msg.add({
            key: 'alarm-filter',
            severity: 'success',
            summary: '提示',
            detail: `${bln ? '启用' : '禁用'}成功`,
          });
          !isArr ? (model.status = bln) : this.fetchData();
        },
        (res) => {
          this.loading = false;
          this.msg.add({
            key: 'alarm-filter',
            severity: 'error',
            summary: '提示',
            detail: res.error.message || res.message,
          });
        },
      );
    }
  }

  /**
   * 删除过滤规则
   * @param id 规则id
   */
  delRule(ids?: any) {
    const isArr = ids instanceof Array;
    const id = !isArr ? ids : ids.filter((el) => !el.status).map((el) => el.id);
    let text = isArr ? '选中' : '此';
    this.dataList == ids && (text = '当前页');
    (!isArr || this.getOperatStatus('del')) &&
      this.confirmationService.confirm({
        key: 'alarm-rule-filter',
        message: `确定删除${text}过滤规则吗?`,
        header: '提示',
        icon: 'icon-exclamation-triangle',
        accept: () => {
          this.delete(id.toString());
        },
      });
  }

  /**
   * 新增批量操作状态码
   * @param type 操作类型 del, ENABLED, DISABLED
   * @returns
   */
  getOperatStatus(type) {
    // 获取当前操作的状态
    const status = {
      del: { k: 'canDelete', n: '删除' },
      ENABLED: { k: 'canEnable', n: '启用' },
      DISABLED: { k: 'canDisabled', n: '停用' },
      add: { k: 'canAddTags', n: '删除' },
    }[type];
    const is = this[status.k];
    is &&
      this.msg.add({
        key: 'alarm-filter',
        severity: 'info',
        summary: '提示',
        detail: `暂无可${status.n}项`,
      });
    return !is;
  }

  /**
   * 数组转字符串
   * @param value 数组字符串
   * @returns
   */
  toString(value: string[]) {
    return (value || []).join(',');
  }

  /**
   * 删除规则
   * @param id id
   */
  delete(ids?: string): void {
    this.loading = true;
    this.alarmFilterService.deleteAlarmFilterRule(ids).subscribe(
      (res) => {
        this.loading = false;
        this.msg.add({
          key: 'alarm-filter',
          severity: 'success',
          summary: '提示',
          detail: `删除成功`,
        });
        // 子规则直接更新子表
        this.fetchData();
      },
      (res) => {
        this.loading = false;
        this.msg.add({
          key: 'alarm-filter',
          severity: 'error',
          summary: '提示',
          detail: res.error.message || res.message,
        });
      },
    );
  }

  /**
   * 导出规则
   * @param isAll 是否全部导出
   */
  export() {
    const ids = this.selectedList.map((el) => el.id);
    const ps = {
      ids: ids.length != 0 ? ids.join(',') : '',
      name: this.searchParams.name,
      status: this.searchParams.status,
    };

    let pathP = '?';
    let split = '';

    // tslint:disable-next-line: forin
    for (const key in ps) {
      if (ps[key]) {
        pathP += split + `${key}=${ps[key]}`;
        split = '&';
      }
    }
    let url = `/${Routingname.R}/alarm-filter/export`;
    exportRule((url += `${pathP}`) + `&principal=${this.tuiUser.curUser.principal}&module=${encodeURI(this.tuiNavSer.curNav.parent?.label)}&subModule=${encodeURI(this.tuiNavSer.curNav.label)}`);
    // window.open();
  }

  /**
   * 初始化导入弹窗
   */
  initDialog(): void {
    this.uploadDom.clear();
    this.uploadData = {
      disabled: true,
      file: null,
      text: '请选择文件',
      progress: false,
      information: null,
      maxHeight: 300,
      tableHeightToScroll: '50px',
      timer: 0,
    };
    this.uploadData.maxHeight = document.documentElement.clientHeight - 200 > 600 ? 600 : document.documentElement.clientHeight - 200;
    this.uploadData.tableHeightToScroll = this.uploadData.maxHeight - 230 + 'px';
  }

  /**
   * 规则导入
   */
  import() {
    this.importRules = true;
  }

  /**
   * 选择文件
   * @param $event
   * @returns
   */
  selectedFile($event): void {
    if (this.uploadDom.msgs.length) {
      this.msg.clear();
      this.msg.add({
        key: 'alarm-filter',
        severity: 'error',
        summary: '提示',
        detail: this.uploadDom.msgs[0].detail,
      });
      return;
    }
    if ($event.files.length) {
      this.uploadData.file = $event.files[0];
      this.uploadData.text = this.uploadData.file.name;
      this.uploadData.disabled = false;
    }
  }

  /**
   * 清除已经选择的文件
   */
  clearFile(): void {
    this.uploadDom.clear();
    this.uploadData.file = null;
    this.uploadData.text = '请选择文件';
    this.uploadData.disabled = true;
  }

  /**
   * 上传文件
   */
  uploadFile(): void {
    this.uploadDom.upload();
  }

  /**
   * 开始上传文件
   */
  beginUpload(): void {
    this.uploadData.progress = true;
    this.uploadData.timer = new Date().getTime();
  }

  /**
   * 文件上传成功
   * @param $event event
   */
  successFile($event): void {
    const lastTimer = new Date().getTime() - this.uploadData.timer;
    if (lastTimer < 2000) {
      setTimeout(() => {
        this.uploadData.progress = false;
      }, 2000 - lastTimer);
    } else {
      this.uploadData.progress = false;
    }
    this.uploadData.disabled = true;
    this.uploadData.information = $event.originalEvent.body;
    if (this.uploadData.information.ruleContent.length > 0) {
      for (let i = 0; i < this.uploadData.information.ruleContent.length; i++) {
        this.uploadData.information.ruleContent[i].overwrite = false;
        this.uploadData.information.ruleContent[i].overskip = true;
        this.uploadData.information.ruleContent[i].againno = false;
      }
    }
    this.search();
  }

  uploadError({ error }): void {
    const lastTimer = new Date().getTime() - this.uploadData.timer;
    if (lastTimer < 2000) {
      setTimeout(() => {
        this.uploadData.progress = false;
        this.msg.clear();
        this.msg.add({
          key: 'alarm-filter',
          severity: 'error',
          summary: '提示',
          detail: error.error.message || error.message,
        });
        this.uploadDom.clear();
        this.uploadData = {
          disabled: true,
          file: null,
          text: '请选择文件',
          progress: false,
          information: null,
          maxHeight: 300,
          tableHeightToScroll: '50px',
          timer: 0,
        };
      }, 2000 - lastTimer);
    } else {
      this.uploadData.progress = false;
      this.msg.clear();
      this.msg.add({
        key: 'alarm-filter',
        severity: 'error',
        summary: '提示',
        detail: error.error.message || error.message,
      });
      this.uploadDom.clear();
      this.uploadData = {
        disabled: true,
        file: null,
        text: '请选择文件',
        progress: false,
        information: null,
        maxHeight: 300,
        tableHeightToScroll: '50px',
        timer: 0,
      };
    }
    this.uploadData.disabled = true;
  }

  /**
   * 关闭导入弹窗
   */
  closeUpload() {
    if (this.uploadData.information?.success) {
      // 子规则直接更新子表
      this.search();
    }

    this.uploadDom.clear();
    this.uploadData = {
      disabled: true,
      file: null,
      text: '请选择文件',
      progress: false,
      information: null,
      maxHeight: 300,
      tableHeightToScroll: '50px',
      timer: 0,
    };
    this.importRules = false;
    this.uploadData.information = null;
  }

  changeAlarmType() {
    this.searchParams.cat2Number = '';
  }
}
