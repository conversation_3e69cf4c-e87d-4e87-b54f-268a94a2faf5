import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';

import { EmptyComponent } from '@worktile/planet';

import { AppLayoutComponent, TuiRouterGuard } from '@tui/frame';

import { Routingname } from './models/route';

const Routers: Routes = [
  {
    path: '',
    component: AppLayoutComponent,
    data: {
      label: '告警聚合',
    },
    children: [
      {
        path: '',
        redirectTo: Routingname.R + '/' + Routingname.R_AR,
        pathMatch: 'full',
      },
      {
        path: Routingname.R + '/' + Routingname.R_AR,
        loadChildren: () => import('./ofmodules/alarm-rule/alarm-rule.module').then((m) => m.AlarmRuleModule),
        data: { label: '告警规则管理', keep: true },
        canActivate: [TuiRouterGuard],
        canActivateChild: [TuiRouterGuard],
      },
      {
        path: Routingname.R + '/' + Routingname.R_AF,
        loadChildren: () => import('./ofmodules/alarm-filter/alarm-filter.module').then((m) => m.AlarmFilterModule),
        data: { label: '告警过滤规则管理', keep: true },
        canActivate: [TuiRouterGuard],
        canActivateChild: [TuiRouterGuard],
      },
      {
        path: Routingname.R + '/config',
        loadChildren: () => import('./ofmodules/alarm-config/alarm-config.module').then((m) => m.AlarmConfigModule),
        data: { label: '内置规则升级', keep: true },
        canActivate: [TuiRouterGuard],
        canActivateChild: [TuiRouterGuard],
      },
    ],
  },
  {
    path: '**',
    component: EmptyComponent,
  },
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forRoot(Routers, {
      useHash: true,
      onSameUrlNavigation: 'reload',
    }),
  ],
  exports: [RouterModule],
})
export class RoutingModule {}
