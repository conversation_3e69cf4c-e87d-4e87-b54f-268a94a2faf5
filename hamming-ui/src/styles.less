@import url("~@tui/component-library/src/style/mixin/mixin"); //  引入统一方法与变量
@import url("./exp.less");

.cursor {
  cursor: pointer;
}

.has-error {
  border-color: #ff4d4f;
}

.error-tips {
  position: absolute;
  padding: 4px;
  font-size: 14px;
  color: #ff4d4f;
}

.tui-form-tips.error {
  color: #ff4d4f;
}

.tui-exp-formlable.required::before {
  display: inline-block;
  margin-right: 4px;
  font-family: "SimSun", sans-serif;
  font-size: 14px;
  line-height: 1;
  color: #ff4d4f;
  content: "*";
}

.overlay-container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.cdk-overlay-pane {
  position: absolute;
  z-index: 1000;
  height: auto;
  padding: 0;
  line-height: 30px;
  pointer-events: auto;
  background-color: #fff;
}

.default-theme .ui-dialog .ui-dialog-content {
  flex: 1;
}

.ui-contextmenu {
  .ui-menuitem-link.ui-state-disabled {
    cursor: not-allowed;
    background: transparent !important;

    .ui-menuitem-text {
      color: #a0a0a0 !important;
    }
  }
}

.link {
  color: @primary-color !important;
}

.cm-line{
  background: #fff;
  background: var(--component-background, #fff);
  color: #6a6a6a;
  font-family: "-apple-system,BlinkMacSystemFont,'Segoe UI','PingFang SC','Hiragino Sans GB','Microsoft YaHei','Helvetica Neue',Helvetica,Arial,sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol'";
  font-size: 14px;
  font-size: var(--font-size, 14px);
  font-weight: 400;
  padding: 0 4px;
}

.suggestion-item {
  display: flex;
  min-width: 300px;
  max-width: 500px;

  .suggestion-label {
    display: block;
    flex: 1;
    padding-right: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .suggestion-description {
    flex: 0;
    flex-basis: auto;
  }
}
