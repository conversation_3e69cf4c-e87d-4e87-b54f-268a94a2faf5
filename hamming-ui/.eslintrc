{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest"}, "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-var-requires": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-this-alias": ["off"], "@typescript-eslint/no-unused-vars": ["error", {"vars": "all", "args": "none"}], "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-explicit-any": "off", "no-useless-escape": "off"}}