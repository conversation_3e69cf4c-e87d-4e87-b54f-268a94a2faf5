const webpack = require('webpack');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const ExtractTextPlugin = require('extract-text-webpack-plugin');
const OptimizeCssAssetsPlugin = require('optimize-css-assets-webpack-plugin');

module.exports = {
  entry: './public_css.js',
  output: {
    filename: 'unused.js',
  },
  plugins: [
    new ExtractTextPlugin('index.css'),
    new OptimizeCssAssetsPlugin(),
    new CopyWebpackPlugin([{
      from: './src/app/ngtui/components/**/**/*.less',
      to: ''
    }, {
      from: './src/styles/ngtui/*.less',
      to: ''
    }, {
      from: './src/styles/style/*.less',
      to: ''
    }])
  ],
  module: {
    rules: [{
        test: /\.css$/,
        use: ExtractTextPlugin.extract({
          fallback: 'style-loader',
          use: 'css-loader'
        })
      },
      {
        test: /\.less$/,
        use: ExtractTextPlugin.extract({
          fallback: 'style-loader',
          use: [{
              loader: "css-loader"
            },
            {
              loader: "less-loader"
            },
          ]
        })
      },
      {
        test: /\.(png|jpg|gif)$/,
        loader: 'url-loader?limit=10000'
      },
      {
        test: /\.(ttf|woff|eot|svg)$/,
        loader: 'file-loader',
        options: {
          name: 'fonts/[name].[ext]?[hash]'
        }
      },
      // {
      //     test: /\.(png|jpg)$/,
      //     loader: 'file-loader',
      //     options: {
      //         name: 'img/[name].[ext]?[hash]'
      //     }
      // }
    ]
  }
};
