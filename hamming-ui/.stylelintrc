{"extends": ["stylelint-config-standard", "stylelint-config-recess-order"], "overrides": [{"files": ["**/*.less"], "customSyntax": "postcss-less"}], "rules": {"selector-class-pattern": null, "font-family-no-missing-generic-family-keyword": null, "comment-empty-line-before": ["always", {"except": ["first-nested"], "ignore": ["stylelint-commands"]}], "no-empty-source": null, "no-duplicate-selectors": null, "no-descending-specificity": null, "selector-pseudo-element-no-unknown": [true, {"ignorePseudoElements": ["ng-deep"]}], "font-family-name-quotes": null, "comment-no-empty": true, "no-invalid-double-slash-comments": null, "number-max-precision": null, "property-no-vendor-prefix": null, "block-no-empty": null}}