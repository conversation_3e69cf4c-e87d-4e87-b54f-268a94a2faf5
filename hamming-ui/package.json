{"name": "hamming", "version": "3.0.0", "scripts": {"ng": "ng", "dev": "node color", "start": "node --max-old-space-size=8000 node_modules/@angular/cli/bin/ng serve --host 0.0.0.0", "build": "node --max-old-space-size=8000 node_modules/@angular/cli/bin/ng build && webpack --config node_modules/@tui/frame/webpack.config.js", "lint:eslint": "eslint --cache --max-warnings 0  \"{src}/app/**/*.{ts}\" --fix", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "packagr": "ng-packagr -p ng-package.json && cd dist && npm pack", "clean": "mvn clean && mvn package && mvn deploy", "postinstall": "cd .. && husky install hamming-ui/.husky ", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "commit": "git-cz", "parse": "lezer-generator src/app/common/lang-code/code.grammar  -o src/app/common/lang-code/parser.js"}, "private": false, "dependencies": {"@angular/animations": "~12.2.0", "@angular/common": "~12.2.0", "@angular/compiler": "~12.2.0", "@angular/core": "~12.2.0", "@angular/forms": "~12.2.0", "@angular/platform-browser": "~12.2.0", "@angular/platform-browser-dynamic": "~12.2.0", "@angular/router": "~12.2.0", "@firmament/libs": "^1.0.1-alpha", "@lezer/lr": "^1.3.13", "@tui/pangu": "^2.3.1-beta", "@worktile/planet": "^12.1.2", "cc-water-marker": "0.0.2", "core-js": "^2.5.4", "@codemirror/autocomplete": "6.11.0", "@codemirror/commands": "6.3.0", "@codemirror/language": "6.9.2", "@codemirror/lint": "6.4.2", "@codemirror/search": "6.5.4", "@codemirror/state": "6.3.1", "@codemirror/view": "6.22.0", "lezer-generator": "^0.13.4", "rxjs": "~6.6.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-builders/custom-webpack": "^12.1.2", "@angular-devkit/build-angular": "~12.2.4", "@angular-eslint/builder": "12.7.0", "@angular-eslint/eslint-plugin": "12.7.0", "@angular-eslint/eslint-plugin-template": "12.7.0", "@angular-eslint/schematics": "12.7.0", "@angular-eslint/template-parser": "12.7.0", "@angular/cli": "~12.2.16", "@angular/compiler-cli": "~12.2.0", "@commitlint/cli": "^17.4.2", "@commitlint/config-conventional": "^17.4.2", "@commitlint/cz-commitlint": "^17.4.2", "@lezer/generator": "^1.5.1", "@ngx-translate/core": "^11.0.1", "@ngx-translate/http-loader": "^4.0.0", "@types/crypto-js": "^4.1.1", "@types/events": "^3.0.0", "@types/jasmine": "~3.8.0", "@types/node": "^12.11.1", "@typescript-eslint/eslint-plugin": "5.51.0", "@typescript-eslint/parser": "5.51.0", "commitizen": "^4.3.0", "conventional-changelog-cli": "^2.2.2", "eslint": "^8.33.0", "eslint-config-prettier": "^8.6.0", "eslint-formatter-pretty": "^4.1.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.3", "jasmine-core": "~3.8.0", "js2wordcloud": "^1.1.12", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "lint-staged": "^12.4.0", "postcss-less": "^6.0.0", "postcss-plugin-namespace": "0.0.3", "prettier": "2.8.4", "stylelint": "~14.16.1", "stylelint-config-recess-order": "^3.1.0", "stylelint-config-standard": "^30.0.1", "tslib": "^2.5.0", "typescript": "~4.3.5", "webpack-assets-manifest": "^5.1.0", "webpack-cli": "^5.0.1"}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}}