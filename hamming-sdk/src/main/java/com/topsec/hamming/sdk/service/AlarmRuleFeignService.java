package com.topsec.hamming.sdk.service;

import com.topsec.hamming.sdk.service.fallbackFactory.AlarmRuleFeignServiceFallbackFactory;
import com.topsec.hamming.web.model.AlarmRuleDataView;
import com.topsec.hamming.web.model.AlarmRuleGroupView;
import com.topsec.hamming.web.model.AlarmRuleView;
import com.topsec.hamming.web.model.PageAlarmRuleView;
import com.topsec.hamming.web.model.ResponseStatus;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@FeignClient(name = "hamming", contextId = "AlarmRuleFeignService", url = "${feign.hamming.url:}", fallbackFactory = AlarmRuleFeignServiceFallbackFactory.class)
public interface AlarmRuleFeignService {

  @RequestMapping(value = "/hamming/alarm-rule", method = RequestMethod.GET)
  ResponseEntity<PageAlarmRuleView> getAlarmRules(@RequestParam(value = "no", required = false, defaultValue = "1") Integer no,
                                                  @RequestParam(value = "size", required = false, defaultValue = "20") Integer size,
                                                  @RequestParam(value = "groupId", required = false) String groupId,
                                                  @RequestParam(value = "cat1Number", required = false) String cat1Number,
                                                  @RequestParam(value = "cat2Number", required = false) String cat2Number,
                                                  @RequestParam(value = "name", required = false) String name,
                                                  @RequestParam(value = "source", required = false) String source,
                                                  @RequestParam(value = "alarmLevel", required = false) String alarmLevel,
                                                  @RequestParam(value = "confidenceLevel", required = false) String confidenceLevel,
                                                  @RequestParam(value = "tag", required = false) String tag,
                                                  @RequestParam(value = "status", required = false) String status,
                                                  @RequestParam(value = "active", required = false) String active,
                                                  @RequestParam(value = "sort", required = false) String sort,
                                                  @RequestParam(value = "sortType", required = false) String sortType);

  @RequestMapping(value = "/hamming/alarm-rule/{id}", method = RequestMethod.GET)
  ResponseEntity<AlarmRuleDataView> getAlarmRule(@PathVariable(value = "id") String id);

  @RequestMapping(value = "/hamming/alarm-rule", method = RequestMethod.POST)
  ResponseEntity<AlarmRuleView> saveAlarmRule(@RequestBody AlarmRuleDataView alarmRuleDataView);

  @RequestMapping(value = "/hamming/alarm-rule/new", method = RequestMethod.POST)
  ResponseEntity<AlarmRuleView> createAlarmRule(@RequestBody AlarmRuleDataView alarmRuleDataView);

  @RequestMapping(value = "hamming/alarm-rule/{ids}/{status}", method = RequestMethod.PATCH)
  ResponseEntity<ResponseStatus> editAlarmRuleStatus(@PathVariable("status") String status, @PathVariable("ids") String ids);

  @RequestMapping(value = "/hamming/alarm-rule/{id}", method = RequestMethod.PUT)
  ResponseEntity<AlarmRuleView> editAlarmRule(@PathVariable("id") String id, @RequestBody AlarmRuleDataView body);

  @RequestMapping(value = "/hamming/alarm-rule/{ids}", method = RequestMethod.DELETE)
  ResponseEntity<ResponseStatus> deleteAlarmRule(@PathVariable("ids") String ids);

  @RequestMapping(value = "/hamming/alarm-rule-group", method = RequestMethod.GET)
  ResponseEntity<List<AlarmRuleGroupView>> getAlarmRuleGroups(@RequestParam(value = "name", required = false) String name, @RequestParam(value = "catNumber", required = false) String catNumber);
}
