package com.topsec.hamming.sdk.service.fallbackFactory;

import com.topsec.hamming.sdk.service.AlarmRuleFeignService;
import com.topsec.hamming.web.model.AlarmRuleDataView;
import com.topsec.hamming.web.model.AlarmRuleGroupView;
import com.topsec.hamming.web.model.AlarmRuleView;
import com.topsec.hamming.web.model.PageAlarmRuleView;
import com.topsec.hamming.web.model.ResponseStatus;

import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.http.ResponseEntity;

import java.util.List;

/**
 * <AUTHOR>
 * 应用重写此失败方法
 */
public class AlarmRuleFeignServiceFallbackFactory implements FallbackFactory<AlarmRuleFeignService> {
  @Override
  public AlarmRuleFeignService create(Throwable cause) {
    return new AlarmRuleFeignService() {
      @Override
      public ResponseEntity<AlarmRuleView> saveAlarmRule(AlarmRuleDataView alarmRuleDataView) {
        return null;
      }

      @Override
      public ResponseEntity<AlarmRuleView> createAlarmRule(AlarmRuleDataView alarmRuleDataView) {
        return null;
      }

      @Override
      public ResponseEntity<ResponseStatus> editAlarmRuleStatus(String status, String ids) {
        return null;
      }

      @Override
      public ResponseEntity<AlarmRuleView> editAlarmRule(String id, AlarmRuleDataView body) {
        return null;
      }

      @Override
      public ResponseEntity<ResponseStatus> deleteAlarmRule(String ids) {
        return null;
      }

      @Override
      public ResponseEntity<List<AlarmRuleGroupView>> getAlarmRuleGroups(String name, String catNumber) {
        return null;
      }

      @Override
      public ResponseEntity<PageAlarmRuleView> getAlarmRules(Integer no, Integer size, String groupId, String cat1Number, String cat2Number, String name, String source, String alarmLevel, String confidenceLevel, String tag, String status, String active, String sort, String sortType) {
        return null;
      }

      @Override
      public ResponseEntity<AlarmRuleDataView> getAlarmRule(String id) {
        return null;
      }
    };
  }
}
