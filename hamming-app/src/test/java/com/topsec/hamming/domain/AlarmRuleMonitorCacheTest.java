package com.topsec.hamming.domain;

import com.topsec.hamming.app.service.impl.AlarmRuleMonitorServiceImpl;
import com.topsec.hamming.message.monitor.AlarmRuleMonitorMessage;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;

import java.util.List;
import java.util.Random;

/**
 * <AUTHOR>
 */
@Slf4j
public class AlarmRuleMonitorCacheTest {
  List<AlarmRuleMonitorMessage> alarmRuleMonitorMessageList = Lists.newLinkedList();

  @Test
  public void test() {
    System.out.println(DateUtil.offset(DateUtil.date(), DateField.DAY_OF_WEEK, 1).getTime());
    System.out.println(DateUtil.offset(DateUtil.date(), DateField.DAY_OF_WEEK, 1));
    System.out.println(DateUtil.date().getTime());
  }

  @Before
  public void init() {
    Random random = new Random(100L);
    for (int i = 1; i <= 5; i++) {
      AlarmRuleMonitorMessage alarmRuleMonitorMessage = new AlarmRuleMonitorMessage();
      if (i % 2 == 1) {
        alarmRuleMonitorMessage.setCurrentWindowTime(DateUtil.offsetMinute(DateUtil.date(System.currentTimeMillis()), 1).getTime());
      } else {
        alarmRuleMonitorMessage.setCurrentWindowTime(System.currentTimeMillis());
      }

      alarmRuleMonitorMessage.setAlarmRuleId(String.valueOf(i));
      alarmRuleMonitorMessage.setSecurityLogVolume(Math.abs(random.nextLong()));
      alarmRuleMonitorMessage.setSecurityAlarmVolume(Math.abs(random.nextLong()));
      alarmRuleMonitorMessageList.add(alarmRuleMonitorMessage);
    }
  }

  @SneakyThrows
  @Test
  public void run() {
    alarmRuleMonitorMessageList.forEach(alarmRuleMonitorMessage -> {
      log.info(alarmRuleMonitorMessage.toString());
    });

    AlarmRuleMonitorServiceImpl monitorService = new AlarmRuleMonitorServiceImpl();
    monitorService.createMonitorMessage(alarmRuleMonitorMessageList);
  }
}
