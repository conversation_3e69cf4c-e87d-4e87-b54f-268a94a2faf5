package com.topsec.hamming.domain;

import com.google.common.base.CaseFormat;
import com.google.common.base.Converter;
import org.apache.avro.Schema;
import org.apache.commons.io.IOUtils;
import org.junit.Test;

import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URI;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class AvroSchemaTest {

  @Test
  public void convertArray() throws IOException {
    String sss = IOUtils.toString(URI.create("http://10.7.211.68:22100/pensieve/resources/types/typesSchema?typesAliasName=security_log"));

    Schema schema = new Schema.Parser().parse(sss);
    List<Schema.Field> fields = schema.getFields().stream().map(field -> {
      Schema.Field result = null;
      if (field.schema().getType() == Schema.Type.ARRAY) {
        Field elementType = null;
        try {
          elementType = field.schema().getClass().getDeclaredField("elementType");
        } catch (NoSuchFieldException e) {
          e.printStackTrace();
        }
        elementType.setAccessible(true);
        try {
          result = new Schema.Field(field.name(), Schema.createUnion(Schema.create(Schema.Type.NULL), (Schema) elementType.get(field.schema())), "", null);
        } catch (IllegalAccessException e) {
          e.printStackTrace();
        }
      } else {
        result = new Schema.Field(field.name(), field.schema(), "", null);
      }
      return result;
    }).collect(Collectors.toList());
    // System.out.println(fields);

    Schema record = Schema.createRecord(schema.getName(), "", schema.getNamespace(), false, fields);

    System.out.println(record.toString(true));
  }

  @Test
  public void xxx(){

    Converter<String, String> stringStringConverter = CaseFormat.LOWER_CAMEL.converterTo(CaseFormat.LOWER_UNDERSCORE);

    String count_top10_agg = stringStringConverter.convert("count_top10_agg");
    System.out.println(count_top10_agg);

  }
}