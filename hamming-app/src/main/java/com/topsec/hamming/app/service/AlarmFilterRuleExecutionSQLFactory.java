package com.topsec.hamming.app.service;

import com.topsec.hamming.app.service.impl.GlobalFilterExecutionFilterSQL;
import com.topsec.hamming.app.service.impl.SpecificFilterExecutionFilterSQL;
import com.topsec.hamming.domain.entity.AlarmFilterRule;
import com.topsec.hamming.domain.vo.SQLType;
import com.topsec.hamming.types.metadata.Field;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 */
public class AlarmFilterRuleExecutionSQLFactory {
  public static AbstractAlarmFilterRuleExecutionSQL createInstance(SQLType type, AlarmFilterRule alarmFilterRule, LinkedHashMap<Field, Field.FieldType> fieldNameAndType) {
    AbstractAlarmFilterRuleExecutionSQL alarmFilterRuleExecutionSQL;
    switch (type) {
      case global_filter:
        alarmFilterRuleExecutionSQL = new GlobalFilterExecutionFilterSQL();
        break;
      case specific:
        alarmFilterRuleExecutionSQL = new SpecificFilterExecutionFilterSQL();
        break;
      default:
        throw new RuntimeException("FilterRule不支持的sql类型" + type);
    }

    alarmFilterRuleExecutionSQL.setAlarmFilterRule(alarmFilterRule);
    alarmFilterRuleExecutionSQL.setSecurityFieldAndType(fieldNameAndType);
    return alarmFilterRuleExecutionSQL;
  }
}
