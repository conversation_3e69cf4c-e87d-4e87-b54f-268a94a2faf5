package com.topsec.hamming.app.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(
    prefix = "alarm-rule"
)
public class AlarmRuleConfig {
  private File file;
  private int maxSize;

  @Data
  public static class File {
    private MergeInfo mergeInfo;
    private boolean enabled;
    private Type type;
  }

  @Data
  public static class Type {
    private String oneLevel;
    private String internal;
  }

  @Data
  public static class MergeInfo {
    private int periodSize;
    private String periodUnit;
  }
}
