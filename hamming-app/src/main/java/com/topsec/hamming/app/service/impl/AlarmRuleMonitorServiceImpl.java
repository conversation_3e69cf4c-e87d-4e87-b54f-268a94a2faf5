package com.topsec.hamming.app.service.impl;

import com.topsec.hamming.app.service.AlarmRuleMonitorService;
import com.topsec.hamming.domain.gateway.repository.AlarmRuleMonitorRepository;
import com.topsec.hamming.message.monitor.AlarmRuleMonitorMessage;
import com.topsec.hamming.types.TimePeriod;
import com.topsec.hamming.types.utils.TimeUtils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AlarmRuleMonitorServiceImpl implements AlarmRuleMonitorService {

  //告警规则告警数量默认统计时间(最近n天)
  private int alarmRuleMonitorStatisticsTime = 7;

  //告警规则统计数据生命周期
  private int alarmRuleMonitorLifeTime = 14;

  @Autowired
  private AlarmRuleMonitorRepository alarmRuleMonitorRepository;

  private Map<String, AlarmRuleMonitorMessage> monitorMap = new HashMap<>();

  private boolean running = false;

  @PostConstruct
  public void initialize() {
    Thread thread = new Thread(new AlarmRuleMonitorConsumer(), AlarmRuleMonitorConsumer.class.getSimpleName());
    running = true;
    thread.start();
  }

  @PreDestroy
  public void destroy() {
    running = false;
  }

  class AlarmRuleMonitorConsumer implements Runnable {

    @Override
    public void run() {
      while (running) {
        List<AlarmRuleMonitorMessage> result;
        synchronized (monitorMap) {
          if (monitorMap.isEmpty()) {
            try {
              monitorMap.wait();
            } catch (InterruptedException e) {
              return;
            }
          }
          result = new LinkedList<>(monitorMap.values());
          monitorMap.clear();
        }
        try {
          for (AlarmRuleMonitorMessage alarmRuleMonitorMessage : result) {
            AlarmRuleMonitorMessage alarmRuleMonitor = alarmRuleMonitorRepository.findByUniqueKey(alarmRuleMonitorMessage.getAlarmRuleId(), alarmRuleMonitorMessage.getCurrentWindowTime());
            if (alarmRuleMonitor != null) {
              alarmRuleMonitorRepository.updateByUniqueKey((alarmRuleMonitorMessage.getSecurityAlarmVolume() + alarmRuleMonitor.getSecurityAlarmVolume()), alarmRuleMonitorMessage.getAlarmRuleId(), alarmRuleMonitorMessage.getCurrentWindowTime());
            } else {
              alarmRuleMonitorRepository.saveAlarmRuleMonitor(alarmRuleMonitorMessage);
            }
          }
        } catch (Throwable e) {
          log.warn("failed to save monitor message", e);//消费异常(数据库异常)，可能会导致数据丢失
        }
      }

    }
  }

  @Override
  public long getTotalMonitorMessage(String alarmRuleId) {
    //使用默认时间进行统计
    long endTime = System.currentTimeMillis();
    long startTime = endTime - alarmRuleMonitorStatisticsTime * 86400 * 1000;
    return alarmRuleMonitorRepository.getMonitorMessageByAlarmRuleId(alarmRuleId, startTime, endTime);
  }

  @Override
  public void createMonitorMessage(List<AlarmRuleMonitorMessage> alarmRuleMonitorMessageList) {
    synchronized (monitorMap){
      for (AlarmRuleMonitorMessage receiveMessage : alarmRuleMonitorMessageList) {
        if (receiveMessage.getSecurityAlarmVolume() < 1 || receiveMessage.getAlarmRuleId() == null || receiveMessage.getCurrentWindowTime() < 1) {
          log.warn("error message: {}", receiveMessage);
        } else {
          String uniqueKey = receiveMessage.getAlarmRuleId() + "_" + receiveMessage.getCurrentWindowTime();
          if (monitorMap.containsKey(uniqueKey)) {
            AlarmRuleMonitorMessage monitorMessage = monitorMap.get(uniqueKey);
            monitorMessage.setSecurityAlarmVolume(monitorMessage.getSecurityAlarmVolume() + receiveMessage.getSecurityAlarmVolume());
            monitorMessage.setSecurityLogVolume(monitorMessage.getSecurityLogVolume() + receiveMessage.getSecurityLogVolume());
          } else {
            monitorMap.put(uniqueKey, receiveMessage);
          }
        }
      }
      monitorMap.notifyAll();
    }
    if (log.isDebugEnabled()) {
      log.debug("接收到规则信息:{}", alarmRuleMonitorMessageList.toString());
    }
  }

  @Override
  public List<AlarmRuleMonitorMessage> getMonitorMessagesIn(TimePeriod recentPeriod) {
    long startTime = TimeUtils.getRecentPeriodStartTime(recentPeriod.toMillis());
    List<AlarmRuleMonitorMessage> historyMonitorMessages = alarmRuleMonitorRepository.getMonitorMessageAfter(startTime);
    return historyMonitorMessages;
  }


  //清理历史数据(每天清理一次)
  @Scheduled(cron = "0 1 0/1 * * ?")
  protected void clearHistoryData() {
    if (log.isInfoEnabled()) {
      log.debug("清理" + alarmRuleMonitorLifeTime + "天前的告警规则统计数据");
    }
    long timeMillis = System.currentTimeMillis() - alarmRuleMonitorLifeTime * 86400 * 1000;
    try {
      alarmRuleMonitorRepository.deleteByTime(timeMillis);
    } catch (Throwable t) {
      log.warn("清理告警规则统计数据异常", t);
    }
  }

}