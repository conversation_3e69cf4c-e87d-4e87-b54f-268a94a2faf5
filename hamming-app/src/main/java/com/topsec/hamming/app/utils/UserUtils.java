package com.topsec.hamming.app.utils;

import com.topsec.hamming.domain.gateway.aas.UserService;

import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class UserUtils {
  @Autowired
  UserService userService;

  /**
   * 获取用户姓名及用户名，格式为：姓名（用户名）
   *
   * @return
   */
  public String getUserNameAndIdentityName() {
    String userName = null;
    String identityName = HttpHeaderUtils.getUserIdentityName();
    String clientPrincipal = HttpHeaderUtils.getClientPrincipal();
    if (!Strings.isNullOrEmpty(clientPrincipal)) {
      userName = userService.getUserName(clientPrincipal);
    }
    return Strings.isNullOrEmpty(userName) ? identityName : userName + "（" + identityName + "）";
  }
}
