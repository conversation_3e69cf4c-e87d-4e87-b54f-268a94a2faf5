package com.topsec.hamming.app.service;

import com.topsec.hamming.domain.entity.AlarmFilterRule;
import com.topsec.hamming.domain.entity.AlarmRule;
import com.topsec.hamming.domain.entity.AlarmRuleGroup;
import com.topsec.hamming.domain.vo.rule.template.RuleFileTemplate;
import com.topsec.hamming.types.Expression;
import com.topsec.hamming.types.http.CheckReport;
import com.topsec.hamming.types.metadata.Field;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface RuleCheckService {
  CheckReport checkGroupNumber(RuleFileTemplate abstractTemplate);

  CheckReport checkNameConflict(RuleFileTemplate abstractTemplate);

  CheckReport checkNameFormat(String name);

  /**
   * 校验告警规则
   *
   * @param alarmRule
   * @return
   */
  CheckReport checkNameConflict(AlarmRule alarmRule);

  CheckReport checkMergeInfo(AlarmRule alarmRule);

  CheckReport checkAlarmInfoFields(Map<Field, Expression.ValueExpression> fieldMap);

  CheckReport checkFilters(AlarmRule alarmRule);

  CheckReport checkNameAndGroupAndMergeAndAlarmInfo(AlarmRule alarmRule);

  CheckReport checkAndCorrectGroupId(String groupNumber, AlarmRule alarmRule);

  /**
   * group
   */
  CheckReport checkGroupNumber(AlarmRuleGroup alarmRuleGroup);


  /**
   * filter-rule
   */
  CheckReport checkNameConflict(AlarmFilterRule alarmFilterRule);

  CheckReport checkDrops(AlarmFilterRule alarmFilterRule);
}
