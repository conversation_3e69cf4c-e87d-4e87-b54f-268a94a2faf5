package com.topsec.hamming.app.service.impl;

import com.topsec.hamming.app.service.AlarmRuleGroupService;
import com.topsec.hamming.app.service.AlarmRuleMonitorService;
import com.topsec.hamming.domain.entity.AlarmRuleGroup;
import com.topsec.hamming.domain.gateway.knowledge.KnowledgeService;
import com.topsec.hamming.domain.gateway.repository.AlarmRuleGroupRepository;

import com.topsec.hamming.infra.convert.AlarmRuleGroupEntityToGroupConverter;
import com.topsec.hamming.infra.gateway.repository.SkipDeprecatedGroupFilter;
import com.topsec.hamming.infra.gateway.repository.jpa.AlarmRuleGroupJpaRepository;
import com.topsec.hamming.infra.gateway.repository.jpa.AlarmRuleJpaRepository;
import com.topsec.hamming.infra.po.AlarmRuleGroupEntity;
import com.topsec.hamming.types.Status;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import javax.persistence.criteria.Predicate;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AlarmRuleGroupServiceImpl implements AlarmRuleGroupService {
  @Autowired
  AlarmRuleGroupRepository groupRepository;
  @Autowired
  AlarmRuleJpaRepository alarmRuleJpaRepository;
  @Autowired
  AlarmRuleGroupJpaRepository groupJpaRepository;
  @Autowired
  AlarmRuleGroupEntityToGroupConverter groupEntityToGroupConverter;

  @Autowired
  AlarmRuleMonitorService alarmRuleMonitorService;
  @Autowired
  KnowledgeService knowledgeService;

  @Override
  @SkipDeprecatedGroupFilter
  @Cacheable(cacheNames = "alarmRuleGroup", key = "#id", unless = "#result==null")
  public AlarmRuleGroup getAlarmRuleGroup(String id) {
    Optional<AlarmRuleGroupEntity> optional = groupJpaRepository.findById(id);
    if (optional.isPresent()) {
      AlarmRuleGroupEntity alarmRuleGroupEntity = optional.get();
      return groupEntityToGroupConverter.convert(alarmRuleGroupEntity);
    }
    return null;
  }

  @Override
  @SkipDeprecatedGroupFilter
  public List<AlarmRuleGroup> getAlarmRuleGroups(HashMap<String, String> keywordMap) {
    Sort sort = Sort.by(Sort.Direction.ASC, "number");
    List<AlarmRuleGroupEntity> alarmRuleGroupEntities = groupJpaRepository.findAll(getSpecification(keywordMap), sort);
    return alarmRuleGroupEntities.stream().map(entity -> groupEntityToGroupConverter.convert(entity)).collect(Collectors.toList());
  }

  private Specification<AlarmRuleGroupEntity> getSpecification(HashMap<String, String> keywordMap) {
    return (root, query, criteriaBuilder) -> {
      if (keywordMap.size() == 0) {
        return criteriaBuilder.and();
      }
      ArrayList<Predicate> predicates = new ArrayList<>();
      for (String key : keywordMap.keySet()) {
        if (!com.google.common.base.Strings.isNullOrEmpty(keywordMap.get(key))) {
          predicates.add(criteriaBuilder.like(root.get(key).as(String.class), /*"%" +*/ keywordMap.get(key)/* + "%"*/));
        }
      }
      return criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()]));
    };
  }

  @Override
  public AlarmRuleGroup createAlarmRuleGroup(AlarmRuleGroup alarmRuleGroup) {
    if (Strings.isNullOrEmpty(alarmRuleGroup.getId())) {
      alarmRuleGroup.setId(UUID.randomUUID().toString());
    }
    alarmRuleGroup.setStatus(Status.ENABLED);
//    addTotalFieldAlarmRuleGroup(Lists.newArrayList(alarmRuleGroup));
    return groupRepository.saveAlarmRuleGroup(alarmRuleGroup);
  }

  @Override
  public void createAlarmRuleGroups(List<AlarmRuleGroup> groups) {
    for (AlarmRuleGroup group : groups) {
      createAlarmRuleGroup(group);
    }
  }

  @Override
  public LinkedHashMap<String, Long> getAlarmRuleGroupStatisticsIn(List<String> alarmRuleIds) {
    return alarmRuleIds.stream()
        .map(ruleId -> alarmRuleJpaRepository.findGroupNameById(ruleId))
        .filter(groupName -> !Strings.isNullOrEmpty(groupName))
        .collect(Collectors.groupingBy(groupName -> groupName, Collectors.counting()))
        .entrySet().stream()
        .sorted(Collections.reverseOrder(Map.Entry.comparingByValue()))
        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v2, LinkedHashMap::new));
  }

  @Override
  public boolean upgradeGroup() {
    //groupJpaRepository.deleteAll();
    List<AlarmRuleGroup> groups = knowledgeService.getCatMapping();
    if (groups.isEmpty()) {
      log.info("Tib服务返回事件分类信息为空，开启dev模式初始化！");
      groups = knowledgeService.getInternalCatMapping();
    }
    createAlarmRuleGroups(groups);
    return true;
  }
}
