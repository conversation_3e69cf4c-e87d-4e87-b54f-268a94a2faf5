package com.topsec.hamming.app.service.impl;

import com.topsec.hamming.app.service.AbstractAlarmFilterRuleExecutionSQL;
import com.topsec.hamming.infra.utils.ExpressionUtil;
import com.topsec.hamming.domain.entity.AlarmFilterRule;
import com.topsec.hamming.message.config.FieldsConstants;
import com.topsec.hamming.types.Expression;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class GlobalFilterExecutionFilterSQL extends AbstractAlarmFilterRuleExecutionSQL {
  @Override
  public String getExecutionSQL() {
    AlarmFilterRule alarmFilterRule = getAlarmFilterRule();
    if (alarmFilterRule != null) {
      String sql = String.format("select %s \nfrom %s", generateCaseWhenSQL(), getTEMPORARY_VIEW());
      log.debug("filterId.{}.global-filter-sql:\n{}\n", alarmFilterRule.getId(), sql);
      return sql;
    }
    return "";
  }

  private String generateCaseWhenSQL() {
    return String.format("case when %s then '%s' \nelse '%s' \nend as analyze_info ", getDropCondition(), getAlarmFilterRule().getId(), FieldsConstants.NOT_MATCH);
  }

  private String getDropCondition() {
    Expression.BooleanExpression dropExpression = getAlarmFilterRule().getDrops();
    if (Strings.isNullOrEmpty(dropExpression.toString())) {
      return ExpressionUtil.generateScalarExpression(dropExpression, getSecurityFieldAndType());
    } else {
      return dropExpression.toString();
    }
  }
}
