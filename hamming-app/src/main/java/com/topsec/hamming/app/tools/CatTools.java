package com.topsec.hamming.app.tools;

import com.topsec.hamming.domain.entity.AlarmRuleGroup;
import com.topsec.hamming.message.config.FieldsConstants;

/**
 * <AUTHOR>
 */
public class CatTools {

 /* public static Expression.BooleanExpression generateCatExpression(String cat2Id, Expression.BooleanExpression expression) {
    if (expression.getNode() != null) {
      //Todo: 考虑全局or的情况
      *//*List<com.topsec.minsky.domain.Expression> children = expression.getNode().getChildren();
      children.add(getCatExpression(cat2Id));*//*
    } else {
      String format = String.format("%s and (%s)", getCatExpressionStr(cat2Id), expression);
      expression = new Expression.BooleanExpression(format);
    }
    return expression;
  }*/

  private static com.topsec.minsky.domain.Expression getCatExpression(String catId) {
    com.topsec.minsky.domain.Expression expression = new com.topsec.minsky.domain.Expression();
    com.topsec.minsky.domain.Expression.LeafNode leafNode = new com.topsec.minsky.domain.Expression.LeafNode();
    leafNode.setLeft(FieldsConstants.CAT);
    leafNode.setOperator("=");
    leafNode.setRight(catId);
    expression.setLeafNode(leafNode);
    return expression;
  }

  public static String getCatExpressionStr(String catId) {
    if (catId.equalsIgnoreCase(AlarmRuleGroup.DefaultGroup.WITHOUT_CAT.getId())) {
      return "1=1";
    } else {
      return String.format(" %s = '%s'", FieldsConstants.CAT, catId);
    }
  }
}
