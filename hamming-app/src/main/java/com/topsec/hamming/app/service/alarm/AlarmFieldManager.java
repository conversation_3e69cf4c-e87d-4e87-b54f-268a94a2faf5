package com.topsec.hamming.app.service.alarm;

import com.topsec.hamming.domain.config.MetadataConfig;
import com.topsec.hamming.domain.gateway.metadata.MetadataService;
import com.topsec.hamming.types.metadata.MetadataConstants;

import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class AlarmFieldManager {
  @Autowired
  private MetadataService metadataService;

  @Autowired
  private MetadataConfig metadataConfig;

  private static List<String> alarmFields;

  public List<String> getFields() {
    //Todo:改为定时更新
    if (alarmFields == null || alarmFields.size() == 0) {
      initAlarmFields();
    }
    return alarmFields;
  }

  private void initAlarmFields() {
    alarmFields = Lists.newArrayList();
    Set<String> alarmEventFieldNames = metadataService.getFields(MetadataConstants.ALARM_EVENT_TOPIC).stream()
        .map(metadataField -> metadataField.getName().toUpperCase())
        .collect(Collectors.toSet());
    alarmFields.addAll(alarmEventFieldNames);
  }
}
