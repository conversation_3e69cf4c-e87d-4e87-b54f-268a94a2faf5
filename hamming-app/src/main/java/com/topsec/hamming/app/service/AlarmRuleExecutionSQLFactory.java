package com.topsec.hamming.app.service;

import com.topsec.hamming.app.service.impl.AlarmRuleExecutionAggSQL;
import com.topsec.hamming.app.service.impl.AlarmRuleExecutionJudgeAfterAggSQL;
import com.topsec.hamming.app.service.impl.AlarmRuleExecutionFilterSQL;
import com.topsec.hamming.domain.entity.AlarmRule;
import com.topsec.hamming.domain.vo.SQLType;
import com.topsec.hamming.types.metadata.Field;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class AlarmRuleExecutionSQLFactory {
  public static AbstractAlarmRuleExecutionSQL createInstance(SQLType type, String cat2Number, AlarmRule alarmRule, Set<String> expandAggFunctions, List<String> intermediateVariables, LinkedHashMap<Field, Field.FieldType> securityFieldAndType) {
    AbstractAlarmRuleExecutionSQL alarmRuleExecutionSQL;
    switch (type) {
      case filter:
        alarmRuleExecutionSQL = new AlarmRuleExecutionFilterSQL();
        break;
      case judge_after_agg:
        alarmRuleExecutionSQL = new AlarmRuleExecutionJudgeAfterAggSQL();
        break;
      case agg:
        alarmRuleExecutionSQL = new AlarmRuleExecutionAggSQL();
        break;
      default:
        throw new RuntimeException("不支持的sql类型" + type);
    }

    alarmRuleExecutionSQL.setCat2Number(cat2Number);
    alarmRuleExecutionSQL.setAlarmRule(alarmRule);
    alarmRuleExecutionSQL.setExpandAggFunctions(expandAggFunctions);
    alarmRuleExecutionSQL.setIntermediateVariables(intermediateVariables);
    alarmRuleExecutionSQL.setSecurityFieldAndType(securityFieldAndType);
    return alarmRuleExecutionSQL;
  }
}
