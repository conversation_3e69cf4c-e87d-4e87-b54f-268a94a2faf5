package com.topsec.hamming.app.service;

import com.topsec.hamming.domain.entity.AlarmFilterRule;
import com.topsec.hamming.message.rule.AlarmRuleExecutionMessage;
import com.topsec.hamming.message.filter.FilterExecutionMessage;
import com.topsec.hamming.types.http.Result;
import com.topsec.hamming.types.metadata.Field;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 */
public interface AlarmFilterRuleExecutionMessageService {

  FilterExecutionMessage generateExecutionMessage(AlarmFilterRule alarmFilterRule, AlarmRuleExecutionMessage.UpdateType updateType);

  Result testExecutionMessage(AlarmFilterRule alarmFilterRule, LinkedHashMap<Field, Field.FieldType> securityFieldAndType);
}
