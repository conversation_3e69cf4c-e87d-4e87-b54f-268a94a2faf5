package com.topsec.hamming.app.utils;

import com.topsec.hamming.app.dto.RuleFile;
import com.topsec.hamming.types.exception.ServiceUnavailableException;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.io.inputstream.ZipInputStream;
import net.lingala.zip4j.io.outputstream.ZipOutputStream;
import net.lingala.zip4j.model.FileHeader;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.model.enums.CompressionMethod;
import org.zeroturnaround.zip.commons.IOUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.List;
import javax.servlet.ServletOutputStream;

/**
 * <AUTHOR>
 */
@Slf4j
public class ZipUtils {

  public static List<RuleFile> unzipRuleFile(ZipFile zipFile, boolean isEncrypt, boolean isSpecialCharSet) {
    List<RuleFile> ruleFiles = Lists.newArrayList();
    if (!zipFile.isValidZipFile()) {
      log.error("压缩包：" + zipFile + "已经损坏!");
      return Lists.newArrayList();
    }
    try {
      List<FileHeader> fileHeaders = zipFile.getFileHeaders();
      for (FileHeader fileHeader : fileHeaders) {
        if (!fileHeader.isDirectory()) {
          ZipInputStream inputStream = zipFile.getInputStream(fileHeader);
          if (!fileHeader.getFileName().endsWith(".json")) {
            log.error("请检查规则包内容，支持规则文件格式为: .json");
          }
          RuleFile ruleFile = new RuleFile();
          //Todo: 处理中文乱码问题
          String fileName = isSpecialCharSet ? new String(fileHeader.getFileName().getBytes("Cp437"), "GBK") : fileHeader.getFileName();
          ruleFile.setFileName(fileName);
          if (isEncrypt) {
            ruleFile.setContent(AESUtils.decrypt(IOUtils.toString(inputStream, "UTF-8")));
          } else {
            ruleFile.setContent(IOUtils.toString(inputStream, "UTF-8"));
          }
          ruleFiles.add(ruleFile);
        }
      }
    } catch (IOException e) {
      log.error(String.format("解压文件：%s失败!", zipFile.getFile()), e);
    }
    return ruleFiles;
  }

  public static List<String> unzipFile(ZipFile zipFile, boolean isEncrypt) {
    List<String> alarmRuleList = Lists.newLinkedList();
    if (!zipFile.isValidZipFile()) {
      log.error("压缩包：" + zipFile + "已经损坏!");
      return alarmRuleList;
    }
    try {
      List<FileHeader> fileHeaders = zipFile.getFileHeaders();
      for (FileHeader fileHeader : fileHeaders) {
        if (!fileHeader.isDirectory()) {
          ZipInputStream inputStream = zipFile.getInputStream(fileHeader);
          if (!fileHeader.getFileName().endsWith(".json")) {
            log.error("请检查规则包内容，支持规则文件格式为: .json");
          }
          if (isEncrypt) {
            alarmRuleList.add(AESUtils.decrypt(IOUtils.toString(inputStream, "UTF-8")));
          } else {
            alarmRuleList.add(IOUtils.toString(inputStream, "UTF-8"));
          }

        }
      }
    } catch (IOException e) {
      log.error(String.format("解压文件：%s失败!", zipFile.getFile()), e);
    }
    return alarmRuleList;
  }

  public static void zipFile(ServletOutputStream outputStream, File srcFile) {
    //设置压缩参数
    ZipParameters zipParameters = new ZipParameters();
    zipParameters.setCompressionMethod(CompressionMethod.STORE);

    File[] fileToAdds = srcFile.listFiles();
    //遍历文件，追加
    try (ZipOutputStream zos = new ZipOutputStream(outputStream)) {
      for (File fileToAdd : fileToAdds) {
        if (fileToAdd.isFile()) {
          writeValue(fileToAdd, zipParameters, zos, "");
        } else { //isDirectory
          String dirName = fileToAdd.getName();  //取文件夹名称
          File[] subFileToAdds = fileToAdd.listFiles();
          for (File subFileToAdd : subFileToAdds) {
            writeValue(subFileToAdd, zipParameters, zos, dirName + "/");
          }
        }
      }
    } catch (IOException e) {
      log.error("文件压缩出错", e);
      throw new ServiceUnavailableException("文件压缩出错!");
    }
  }

  private static void writeValue(File addFile, ZipParameters zipParameters, ZipOutputStream zos, String parentPath) {
    byte[] buff = new byte[4096];
    int readLen;
    try {
      // Entry size has to be set if you want to add entries of STORE compression method (no compression)
      // This is not required for deflate compression
      if (zipParameters.getCompressionMethod() == CompressionMethod.STORE) {
        zipParameters.setEntrySize(addFile.length());
      }
      zipParameters.setFileNameInZip(parentPath + addFile.getName());
      zos.putNextEntry(zipParameters);

      try (InputStream inputStream = Files.newInputStream(addFile.toPath())) {
        while ((readLen = inputStream.read(buff)) != -1) {
          zos.write(buff, 0, readLen);
        }
      }
      zos.closeEntry();
    } catch (IOException e) {
      log.error("文件压缩出错", e);
      throw new ServiceUnavailableException("文件压缩出错!");
    }
  }
}
