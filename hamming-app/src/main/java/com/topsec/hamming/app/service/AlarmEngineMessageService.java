package com.topsec.hamming.app.service;

import com.topsec.hamming.domain.entity.AlarmFilterRule;
import com.topsec.hamming.domain.entity.AlarmRule;
import com.topsec.hamming.message.AlarmEngineMessage;
import com.topsec.hamming.message.CloseAlarmMessage;
import com.topsec.hamming.message.rule.AlarmRuleExecutionMessage;
import com.topsec.hamming.message.monitor.AlarmRuleMonitorMessage;
import com.topsec.hamming.message.monitor.AlarmRuleMonitorTriggerMessage;
import com.topsec.hamming.message.filter.FilterExecutionMessage;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AlarmEngineMessageService {
  AlarmRuleExecutionMessage builtAlarmRuleExecutionMessage(AlarmRule alarmRule);

  AlarmRuleExecutionMessage getDefaultAlarmRuleExecutionMessages();

  AlarmEngineMessage getAlarmControlMessage(String type);

  List<CloseAlarmMessage> getCloseAlarmMessages();

  void uploadAlarmRuleMonitor(List<AlarmRuleMonitorMessage> alarmRuleMonitorMessageList);

  void uploadRuleTriggerMonitor(List<AlarmRuleMonitorTriggerMessage> alarmRuleMonitorTriggerMessages);

  FilterExecutionMessage builtFilterExecutionMessage(AlarmFilterRule alarmFilterRule, FilterExecutionMessage.FilterType filterType);
}