package com.topsec.hamming.app.service.impl;

import com.topsec.hamming.app.service.AlarmRuleExecutionMessageService;
import com.topsec.hamming.app.service.AlarmRuleExecutionSQLFactory;
import com.topsec.hamming.app.service.alarm.AlarmFieldManager;
import com.topsec.hamming.app.tools.AlarmRuleExecutionMessageTools;
import com.topsec.hamming.domain.entity.AlarmRule;
import com.topsec.hamming.domain.gateway.engine.CodeService;
import com.topsec.hamming.domain.service.ExpendAggService;
import com.topsec.hamming.domain.vo.SQLType;
import com.topsec.hamming.infra.gateway.repository.jpa.AlarmRuleGroupJpaRepository;
import com.topsec.hamming.message.rule.AlarmRuleExecutionMessage;
import com.topsec.hamming.message.CodeInfo;
import com.topsec.hamming.types.alarm.AlarmRuleSource;
import com.topsec.hamming.types.Expression;
import com.topsec.hamming.types.http.Result;
import com.topsec.hamming.types.metadata.Field;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("alarmRuleExecutionMessageExternalService")
public class AlarmRuleExecutionMessageExternalImpl implements AlarmRuleExecutionMessageService {
  @Autowired
  private ExpendAggService expendAggService;

  @Autowired
  private CodeService codeService;

  @Autowired
  private AlarmFieldManager alarmFieldManager;

  @Autowired
  private AlarmRuleGroupJpaRepository groupJpaRepository;

  private Map<String, String> expendAggInfoMapping;

  @PostConstruct
  private void init() {
    //expandAggFunctions = expendAggService.getExpandAggFunctions();
    expendAggInfoMapping = expendAggService.getExpandAggInfoMapping();
  }

  @Override
  public AlarmRuleExecutionMessage generateExecutionMessage(AlarmRule alarmRule, AlarmRuleExecutionMessage.UpdateType updateType, LinkedHashMap<Field, Field.FieldType> securityFieldAndType) {
    Map<String, String> fieldMapResult = new HashMap<>();
    alarmRule.getFieldMap().forEach((field, expression) -> {
      if (!(expression instanceof Expression.ConditionExpression)) {
        fieldMapResult.put(field.getOriginalFieldName(), expression.toString());
      }
    });
    Map<String, String> expendMapResult = expendAggInfoMapping.entrySet().stream().filter(entry -> !fieldMapResult.containsKey(entry.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (a, b) -> b));
    Set<String> expandAggFunctions = expendAggService.getExpandAggFunctions(expendMapResult);


    AlarmRuleExecutionMessageTools.processIntermediateVariable(alarmRule, alarmFieldManager.getFields());
    List<String> intermediateVariables = AlarmRuleExecutionMessageTools.getIntermediateVariables(alarmRule, alarmFieldManager.getFields());

    String cat2Number = groupJpaRepository.findNumberById(alarmRule.getAlarmRuleGroupId());

    CodeInfo filterCodeInfo = null;
    CodeInfo aggCodeInfo = null;
    CodeInfo caseCodeInfo = null;
    boolean containCaseCode = false;

    try {
      String filterSQL = AlarmRuleExecutionSQLFactory.createInstance(SQLType.filter, cat2Number, alarmRule, expandAggFunctions, intermediateVariables, securityFieldAndType).getExecutionSQL();
      filterCodeInfo = codeService.getFlinkGeneratedClass(filterSQL);
      if (filterCodeInfo == null) {
        throw new RuntimeException(String.format("alarmRule.%s generate filter-message is empty!", alarmRule.getId()));
      }
      String aggSQL = AlarmRuleExecutionSQLFactory.createInstance(SQLType.agg, cat2Number, alarmRule, expandAggFunctions, intermediateVariables, securityFieldAndType).getExecutionSQL();
      aggCodeInfo = codeService.getFlinkGeneratedClass(aggSQL);
      if (aggCodeInfo == null) {
        throw new RuntimeException(String.format("alarmRule.%s generate agg-message is empty!", alarmRule.getId()));
      }
      String judgmentAfterAggSQL = AlarmRuleExecutionSQLFactory.createInstance(SQLType.judge_after_agg, cat2Number, alarmRule, expandAggFunctions, intermediateVariables, securityFieldAndType).getExecutionSQL();
      containCaseCode = !judgmentAfterAggSQL.isEmpty();
      if (containCaseCode) {
        caseCodeInfo = codeService.getFlinkGeneratedClass(judgmentAfterAggSQL);
        if (caseCodeInfo == null) {
          throw new RuntimeException(String.format("alarmRule.%s generate case-message is empty!", alarmRule.getId()));
        }
      }
    } catch (Exception e) {
      throw new RuntimeException(String.format("alarmRule.%s failed to generate message! %s", alarmRule.getId(), e.getMessage()));
    }

    AlarmRuleExecutionMessage executionMessage = new AlarmRuleExecutionMessage(alarmRule.getId(), alarmRule.getName(), cat2Number, updateType, alarmRule.getMergePeriod().toMillis(), alarmRule.getMergeCount());
    executionMessage.setFilterCodeInfo(filterCodeInfo);
    executionMessage.setCaseCodeInfo(caseCodeInfo);
    executionMessage.setAggCodeInfo(aggCodeInfo);
    fieldMapResult.putAll(expendMapResult);
    executionMessage.setAggInfoMapping(fieldMapResult);
    return executionMessage;
  }

  @Override
  public String getGenerateType() {
    return AlarmRuleSource.CUSTOM.getName();
  }

  @Override
  public Result testExecutionMessage(AlarmRule alarmRule, LinkedHashMap<Field, Field.FieldType> securityFieldAndType) {
    try {
      AlarmRuleExecutionMessageTools.processIntermediateVariable(alarmRule, alarmFieldManager.getFields());
      List<String> intermediateVariables = AlarmRuleExecutionMessageTools.getIntermediateVariables(alarmRule, alarmFieldManager.getFields());
      String cat2Number = groupJpaRepository.findNumberById(alarmRule.getAlarmRuleGroupId());
      if (Strings.isNullOrEmpty(cat2Number)){
        return Result.failed(Result.Status.FAILED, "告警类型不存在！");
      }

      Result result = codeService.testFlinkSql(AlarmRuleExecutionSQLFactory.createInstance(SQLType.filter, cat2Number, alarmRule, expendAggService.getExpandAggFunctions(), intermediateVariables, securityFieldAndType).getExecutionSQL());
      if (result.getStatus() == Result.Status.FAILED) {
        result.setMessage("匹配条件语法错误，请检查规则配置！");
        return result;
      }
      result = codeService.testFlinkSql(AlarmRuleExecutionSQLFactory.createInstance(SQLType.agg, cat2Number, alarmRule, expendAggService.getExpandAggFunctions(), intermediateVariables, securityFieldAndType).getExecutionSQL());
      if (result.getStatus() == Result.Status.FAILED) {
        result.setMessage("归并条件语法错误，请检查规则配置！");
        return result;
      }
    } catch (Exception e) {
      log.error("Failed to process intermediate variables!", e);
      return Result.failed(Result.Status.FAILED, "服务异常！", e.getMessage());
    }
    return Result.success();
  }
}
