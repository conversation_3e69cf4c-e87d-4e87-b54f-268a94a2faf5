package com.topsec.hamming.app.service.impl;

import com.topsec.common.utils.ToolUtils;
import com.topsec.hamming.app.service.AlarmFilterRuleExecutionMessageService;
import com.topsec.hamming.app.service.AlarmFilterRuleExecutionSQLFactory;
import com.topsec.hamming.app.utils.ExpressionCheckUtil;
import com.topsec.hamming.domain.config.MetadataConfig;
import com.topsec.hamming.domain.entity.AlarmFilterRule;
import com.topsec.hamming.domain.gateway.engine.CodeService;
import com.topsec.hamming.domain.gateway.metadata.MetadataService;
import com.topsec.hamming.domain.vo.SQLType;
import com.topsec.hamming.message.rule.AlarmRuleExecutionMessage;
import com.topsec.hamming.message.CodeInfo;
import com.topsec.hamming.message.filter.FilterExecutionMessage;
import com.topsec.hamming.types.Expression;
import com.topsec.hamming.types.http.Result;
import com.topsec.hamming.types.metadata.Field;
import com.topsec.hamming.types.metadata.MetadataConstants;
import com.topsec.hamming.types.utils.MetadataUtils;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AlarmFilterRuleExecutionMessageServiceImpl implements AlarmFilterRuleExecutionMessageService {
  @Autowired
  private CodeService codeService;
  @Autowired
  MetadataService metadataService;
  @Autowired
  MetadataConfig metadataConfig;

  @Override
  public FilterExecutionMessage generateExecutionMessage(AlarmFilterRule alarmFilterRule, AlarmRuleExecutionMessage.UpdateType updateType) {
    FilterExecutionMessage.FilterType filterType = alarmFilterRule.getFilterType();
    CodeInfo codeInfo = null;
    try {
      switch (filterType) {
        case SPECIFIC:
          String specificFilterSQL = generateSQL(alarmFilterRule, SQLType.specific);
          codeInfo = codeService.getFlinkGeneratedClass(specificFilterSQL);
          break;
        case ANY:
          String globalFilterSQL = generateSQL(alarmFilterRule, SQLType.global_filter);
          codeInfo = codeService.getFlinkGeneratedClass(globalFilterSQL);
          break;
      }
    } catch (Exception e) {
      throw new RuntimeException(String.format("alarmFilterRule.%s failed to generate message! %s", alarmFilterRule.getId(), e.getMessage()));
    }

    if (codeInfo == null) {
      throw new RuntimeException(String.format("alarmFilterRule.%s generate %s-message is empty!", alarmFilterRule.getId(), filterType));
    }

    return new FilterExecutionMessage(alarmFilterRule.getId(), alarmFilterRule.getCat(), codeInfo, AlarmRuleExecutionMessage.UpdateType.ENABLED, filterType);
  }

  private String generateSQL(AlarmFilterRule alarmFilterRule, SQLType sqlType) {
    LinkedHashMap<Field, Field.FieldType> fieldAndType = metadataService.getFieldAndType(MetadataConstants.SECURITY_LOG_TOPIC, false, false);
    try {
      if (!Strings.isNullOrEmpty(alarmFilterRule.getDrops().toString())) {
        List<String> arrayFields = MetadataUtils.getArrayFields(fieldAndType);
        Expression.BooleanExpression correctAdvancedExpression = ExpressionCheckUtil.checkAndGetCorrectAdvancedExpression(alarmFilterRule.getDrops(), arrayFields);
        alarmFilterRule.setDrops(correctAdvancedExpression);
      }/* else {
        ExpressionCheckUtil.checkBasicExpression(alarmFilterRule.getDrops());
      }*/
    } catch (Exception e) {
      throw new RuntimeException(String.format("过滤规则（%s）的筛选条件（%s）有语法错误！", alarmFilterRule.getName(), alarmFilterRule.getDrops().toString()), e);
    }
    return AlarmFilterRuleExecutionSQLFactory.createInstance(sqlType, alarmFilterRule, fieldAndType).getExecutionSQL();
  }


  @Override
  public Result testExecutionMessage(AlarmFilterRule alarmFilterRule, LinkedHashMap<Field, Field.FieldType> securityFieldAndType) {
    FilterExecutionMessage.FilterType filterType = alarmFilterRule.getFilterType();
    try {
      String sql = null;
      switch (filterType) {
        case SPECIFIC:
          sql = AlarmFilterRuleExecutionSQLFactory.createInstance(SQLType.specific, alarmFilterRule, securityFieldAndType).getExecutionSQL();
          break;
        case ANY:
          sql = AlarmFilterRuleExecutionSQLFactory.createInstance(SQLType.global_filter, alarmFilterRule, securityFieldAndType).getExecutionSQL();
          break;
      }
      Result result = codeService.testFlinkSql(sql);
      if (result.getStatus() == Result.Status.FAILED) {
        result.setMessage("过滤条件语法错误，请检查规则配置！");
        return result;
      }
    } catch (Exception e) {
      return Result.failed(Result.Status.FAILED, "服务异常！", e.getMessage());
    }
    return Result.success();
  }
}
