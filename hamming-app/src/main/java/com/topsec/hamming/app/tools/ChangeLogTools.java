package com.topsec.hamming.app.tools;

import com.topsec.hamming.domain.entity.AlarmFilterRule;
import com.topsec.hamming.domain.entity.AlarmRule;
import com.topsec.hamming.domain.vo.ChangeLogVo;

import org.springframework.data.domain.Sort;

import java.util.Comparator;

/**
 * <AUTHOR>
 */
public class ChangeLogTools {

  public static Comparator<?> getComparator(Sort.Order order, Class<?> aClass) {
    if (order.getProperty().equals(ChangeLogVo.CHANGE_TIME.getFieldName())) {
      return getChangeTimeComparator(order, aClass);
    }
    if (order.getProperty().equals(ChangeLogVo.CHANGE_NAME.getFieldName())) {
      return getChangeNameComparator(order, aClass);
    }

    return null;
  }

  private static Comparator<?> getChangeTimeComparator(Sort.Order order, Class<?> aClass) {
    if (order.getDirection() == org.springframework.data.domain.Sort.Direction.ASC) {
      if (aClass.equals(AlarmRule.class)) {
        return Comparator.<AlarmRule, Long>comparing(rule -> rule.getChangeLog().getChangeTime());
      }
      if (aClass.equals(AlarmFilterRule.class)) {
        return Comparator.<AlarmFilterRule, Long>comparing(rule -> rule.getChangeLog().getChangeTime());
      }
    } else {
      if (aClass.equals(AlarmRule.class)) {
        return Comparator.<AlarmRule, Long>comparing(rule -> rule.getChangeLog().getChangeTime(), Comparator.reverseOrder());
      }
      if (aClass.equals(AlarmFilterRule.class)) {
        return Comparator.<AlarmFilterRule, Long>comparing(rule -> rule.getChangeLog().getChangeTime(), Comparator.reverseOrder());
      }
    }
    return null;
  }

  private static Comparator<?> getChangeNameComparator(Sort.Order order, Class<?> aClass) {
    if (order.getDirection() == org.springframework.data.domain.Sort.Direction.ASC) {
      if (aClass.equals(AlarmRule.class)) {
        return Comparator.<AlarmRule, String>comparing(rule -> rule.getChangeLog().getChangeName());
      }
      if (aClass.equals(AlarmFilterRule.class)) {
        return Comparator.<AlarmFilterRule, String>comparing(rule -> rule.getChangeLog().getChangeName());
      }
    } else {
      if (aClass.equals(AlarmRule.class)) {
        return Comparator.<AlarmRule, String>comparing(rule -> rule.getChangeLog().getChangeName(), Comparator.reverseOrder());
      }
      if (aClass.equals(AlarmFilterRule.class)) {
        return Comparator.<AlarmFilterRule, String>comparing(rule -> rule.getChangeLog().getChangeName(), Comparator.reverseOrder());
      }
    }
    return null;
  }
}
