package com.topsec.hamming.app.service;

import com.topsec.hamming.domain.entity.AlarmRuleGroup;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface AlarmRuleGroupService {
  AlarmRuleGroup getAlarmRuleGroup(String id);

  List<AlarmRuleGroup> getAlarmRuleGroups(HashMap<String, String> keywordMap);

  AlarmRuleGroup createAlarmRuleGroup(AlarmRuleGroup alarmRuleGroup);

  void createAlarmRuleGroups(List<AlarmRuleGroup> groups);

  LinkedHashMap<String, Long> getAlarmRuleGroupStatisticsIn(List<String> alarmRuleIds);

  boolean upgradeGroup();
}
