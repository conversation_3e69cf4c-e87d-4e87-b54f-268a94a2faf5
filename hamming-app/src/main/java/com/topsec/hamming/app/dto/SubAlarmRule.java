package com.topsec.hamming.app.dto;

import com.topsec.common.utils.ToolUtils;
import com.topsec.hamming.app.convert.AlarmRuleConverter;
import com.topsec.hamming.domain.entity.AlarmRule;
import com.topsec.hamming.domain.service.ExpressionGateway;
import com.topsec.hamming.message.config.FieldsConstants;
import com.topsec.hamming.types.alarm.AlarmRuleSource;
import com.topsec.hamming.types.ChangeLog;
import com.topsec.hamming.types.Expression;
import com.topsec.hamming.types.metadata.Field;
import com.topsec.hamming.types.TimePeriod;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
/**
 *  一级平台提供的告警规则-子规则
 */
public class SubAlarmRule implements AlarmRuleConverter {
  private String name;
  private List<String> filters;
  private List<String> group;
  /**
   * 填充策略优先级高于父
   */
  private Map<String, Object> fields;
  private ParentAlarmRule parentAlarmRule;

  @Override
  public AlarmRule convertToAlarmRule() {
    ChangeLog changeLog = new ChangeLog("", System.currentTimeMillis(), ChangeLog.OperationType.CREATE, 1);

    String expression = FieldsConstants.CAT + " = '" + parentAlarmRule.getId() + "'";
    List<String> filters = getFilters();
    if (filters != null && filters.size() > 0) {
      String filterExpressionStr = generateExpressionStr(filters);
      expression = String.format("%s and (%s)", expression, filterExpressionStr);
    }

    Expression.BooleanExpression dropExpression = parentAlarmRule.getDrops() == null || parentAlarmRule.getDrops().size() == 0 ? null : new Expression.BooleanExpression(generateExpressionStr(parentAlarmRule.getDrops()));

    AlarmRule.Builder builder = new AlarmRule.Builder(Strings.isNullOrEmpty(name) ? "default" : name,
        AlarmRuleSource.BUILT_IN,
        dropExpression,
        new Expression.BooleanExpression(expression),
        getFieldMap(),
        getGroup().stream().map(Field::new).collect(Collectors.toList()),
        changeLog,
        0,
        new TimePeriod(1, TimeUnit.DAYS)
    );
    return builder.build();
  }

  private Map<Field, Expression.ValueExpression> getFieldMap() {
    HashMap<String, Object> allFieldMap = Maps.newHashMap(this.parentAlarmRule.getFields());
    allFieldMap.putAll(this.fields);
    Map<Field, Expression.ValueExpression> map = allFieldMap.keySet().stream()
        .filter(fields -> !(fields.equalsIgnoreCase("start_time") || fields.equalsIgnoreCase("end_time")))
        .collect(Collectors.toMap(Field::new, fieldName -> {
          String expression = allFieldMap.get(fieldName).toString();
          if (expression.startsWith("[") && expression.endsWith("]")) {
            try {
              expression = ToolUtils.OBJECT_MAPPER.writeValueAsString(allFieldMap.get(fieldName));
            } catch (JsonProcessingException e) {
              e.printStackTrace();
            }
          }
          return ExpressionGateway.createValueExpression(expression);
        }
    ));
    return map;
  }

  private String generateExpressionStr(List<String> list) {
    if (list == null || list.size() == 0) {
      return null;
    }
    StringBuilder builder = new StringBuilder();
    for (int i = 0; i < list.size(); i++) {
      String str = list.get(i);
      if (str.toLowerCase().contains("and")) {
        str = String.format("(%s)", str);
      }
      builder.append(str);
      if (i < list.size() - 1) {
        builder.append(" or ");
      }
    }
    return builder.toString();
  }

  @Override
  public String toString() {
    return "SubAlarmRule{" +
        "name='" + name + '\'' +
        ", filters=" + filters +
        ", group=" + group +
        ", fields=" + fields +
        '}';
  }
}
