package com.topsec.hamming.app.service;

import com.topsec.hamming.domain.entity.AlarmRule;
import com.topsec.hamming.types.metadata.Field;

import lombok.Data;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
public abstract class AbstractAlarmRuleExecutionSQL extends AbstractExecutionSQL {
  private String cat2Number;
  private AlarmRule alarmRule;
  private Set<String> expandAggFunctions;
  private List<String> intermediateVariables;

  public abstract String getExecutionSQL();

  public String getExpressionFormat(Field mappingField) {
    Field.FieldType fieldType = getMappingFieldType(mappingField);
    switch (fieldType) {
      case STRING:
        return "'%s'";
      case INT:
      case LONG:
      case DOUBLE:
        return "%s";
      default:
        throw new RuntimeException("Illegal type: " + fieldType.getName());
    }
  }

  public String getConcatWSFormat(Field mergeField) {
    Field.FieldType fieldType = getMappingFieldType(mergeField);
    switch (fieldType) {
      case STRING:
        return "%s";
      case INT:
      case LONG:
      case DOUBLE:
        return "cast(%s as varchar)";
      case ARRAY_INT:
      case ARRAY_STRING:
        return "concat_in_array(%s)";
      default:
        throw new RuntimeException("Illegal type: " + fieldType.getName());
    }
  }

  public String getArrayFormat(List<Field> fields) {
    if (fields == null || fields.size() == 0) {
      return "array['']";
    }
    List<String> list = fields.stream().map(field -> String.format("'%s'", field.getOriginalFieldName())).collect(Collectors.toList());
    return String.format("array[%s]", String.join(",", list));
  }

}
