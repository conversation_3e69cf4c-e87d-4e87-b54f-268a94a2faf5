package com.topsec.hamming.app.tools;

import com.topsec.common.utils.FileUtil;
import com.topsec.hamming.app.dto.RuleFile;
import com.topsec.hamming.app.utils.ZipUtils;
import com.topsec.hamming.types.exception.RequestErrorWithStackTraceException;

import com.google.common.collect.Lists;
import net.lingala.zip4j.ZipFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RuleFileTools {
  public static List<RuleFile> unzipRuleFile(MultipartFile multipartFile, boolean idEncrypt, boolean isSpecialCharSet){
    List<RuleFile> ruleFiles = Lists.newArrayList();
    try {
      File file = new File(System.getProperty("java.io.tmpdir"), System.currentTimeMillis() + "-2");
      multipartFile.transferTo(file);
      ZipFile zipFile = new ZipFile(file);
      ruleFiles = ZipUtils.unzipRuleFile(zipFile, idEncrypt, isSpecialCharSet);
      FileUtil.deleteFile(file);
    } catch (IOException e) {
      throw new RequestErrorWithStackTraceException("请上传正确的zip压缩文件！");
    }
    return ruleFiles;
  }

}
