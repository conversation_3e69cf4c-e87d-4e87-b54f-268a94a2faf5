package com.topsec.hamming.app.service;

import com.topsec.hamming.domain.entity.AlarmRule;
import com.topsec.hamming.message.rule.AlarmRuleExecutionMessage;
import com.topsec.hamming.types.http.Result;
import com.topsec.hamming.types.metadata.Field;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 */
public interface AlarmRuleExecutionMessageService {
  AlarmRuleExecutionMessage generateExecutionMessage(AlarmRule alarmRule, AlarmRuleExecutionMessage.UpdateType updateType, LinkedHashMap<Field, Field.FieldType> securityFieldAndType);

  //AlarmRuleExecutionMessage getExecutionMessage(String id);

  String getGenerateType();

  Result testExecutionMessage(AlarmRule alarmRule, LinkedHashMap<Field, Field.FieldType> securityFieldAndType);
}

