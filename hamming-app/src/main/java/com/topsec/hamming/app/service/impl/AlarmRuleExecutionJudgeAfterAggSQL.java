package com.topsec.hamming.app.service.impl;

import com.topsec.hamming.app.service.AbstractAlarmRuleExecutionSQL;
import com.topsec.hamming.message.config.FieldsConstants;
import com.topsec.hamming.types.Expression;
import com.topsec.hamming.types.metadata.Field;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class AlarmRuleExecutionJudgeAfterAggSQL extends AbstractAlarmRuleExecutionSQL {
  @Override
  public String getExecutionSQL() {
    Map<Field, Expression.ValueExpression> judgmentFieldAndConditionExpressionMap = getAlarmRule().getFieldMap().entrySet().stream().filter(entry -> {
      return entry.getValue() instanceof Expression.ConditionExpression;
    }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    if (judgmentFieldAndConditionExpressionMap.size() == 0) {
      return "";
    }

    String selectField = getSelectField(judgmentFieldAndConditionExpressionMap);
    String sql = String.format("select %s \nfrom %s\n", selectField, getTEMPORARY_VIEW());

    log.debug("ruleId.{}.judge-after-agg-sql:\n{}\n", getAlarmRule().getId(), sql);
    return sql;
  }

  private String getSelectField(Map<Field, Expression.ValueExpression> judgmentFieldAndConditionExpressionMap) {
    HashMap<Field, String> judgmentFiledAndCondition = getJudgmentFieldAndConditions(judgmentFieldAndConditionExpressionMap);
    Set<Field> judgmentFields = judgmentFieldAndConditionExpressionMap.keySet();
    /*Set<Field> securityFields = getSecurityFieldAndType().keySet();
    List<String> selectFields = securityFields.stream().map(securityField -> {
      if (judgmentFields.contains(securityField)) {
        return judgmentFiledAndCondition.get(securityField);
      }
      return securityField.getFieldName();
    }).collect(Collectors.toList());*/
    List<String> selectFields = new ArrayList<>(judgmentFiledAndCondition.values());

    List<Field> mappingFieldsOfStringType = judgmentFields.stream().filter(field -> getMappingFieldType(field).equals(Field.FieldType.STRING) || getMappingFieldType(field).equals(Field.FieldType.INT)).collect(Collectors.toList());
    String restructureMappingFields = getArrayFormat(mappingFieldsOfStringType);
    selectFields.add(restructureMappingFields);
    return String.format("%s", String.join(",", selectFields));
  }

  private HashMap<Field, String> getJudgmentFieldAndConditions(Map<Field, Expression.ValueExpression> judgmentFieldAndConditionExpressionMap) {
    HashMap<Field, String> map = new HashMap<>();
    for (Map.Entry<Field, Expression.ValueExpression> entry : judgmentFieldAndConditionExpressionMap.entrySet()) {
      Field field = entry.getKey();
      Map<Expression.BooleanExpression, Expression.ValueExpression> conditionAndExpressionMap = ((Expression.ConditionExpression) entry.getValue()).getConditions();
      String judgmentCondition = generateJudgmentConditionsSQL(field, conditionAndExpressionMap);
      map.put(field, judgmentCondition);
    }
    return map;
  }

  private String generateJudgmentConditionsSQL(Field mappingField, Map<Expression.BooleanExpression, Expression.ValueExpression> conditionAndExpressionMap) {
    Map<String, String> conditionAndReturnValue = getConditionAndReturnValue(conditionAndExpressionMap, getExpressionFormat(mappingField));
    StringBuilder builder = new StringBuilder("case ");
    String elseSQL = null;
    for (Map.Entry<String, String> entry : conditionAndReturnValue.entrySet()) {
      if (entry.getKey().isEmpty()) {
        elseSQL = String.format("else %s ", entry.getValue());
      } else {
        String format = String.format("when %s then %s ", entry.getKey(), entry.getValue());
        builder.append(format);
      }
    }
    if (Strings.isNullOrEmpty(elseSQL)) {
      elseSQL = generateElseSQL(mappingField);
    }
    builder.append(elseSQL);
    builder.append(String.format("end as %s", mappingField));
    return builder.toString();
  }

  private String generateElseSQL(Field mappingField) {
    return String.format("else %s ", mappingField.getOriginalFieldName());
  }

  /**
   * 获取单个映射字段的条件赋值信息
   *
   * @param conditionAndExpressionMap
   * @return "a>10",''
   */
  private Map<String, String> getConditionAndReturnValue(Map<Expression.BooleanExpression, Expression.ValueExpression> conditionAndExpressionMap, String expressionFormat) {
    HashMap<String, String> conditionAndReturnMap = new LinkedHashMap<>();
    for (Expression.BooleanExpression condition : conditionAndExpressionMap.keySet()) {
      String newCondition = restructureIntermediateVariable(condition.toString().isEmpty() ? "1=1" : condition.toString());
      String expression = String.format(expressionFormat, conditionAndExpressionMap.get(condition).toString());
      conditionAndReturnMap.put(newCondition, expression);
    }
    return conditionAndReturnMap;
  }

  private String restructureIntermediateVariable(String condition) {
    for (String variable : getIntermediateVariables()) {
      if (condition.contains(variable)) {
        condition = condition.replace(variable, String.format("%s['%s']", FieldsConstants.AGG_RESULT_MAP, variable));
      }
    }
    return condition;
  }
}
