package com.topsec.hamming.app.utils;

import static com.topsec.hamming.types.config.CommonConstant.CIS_MULTI_TENANT_ENABLED;

import com.topsec.common.utils.ToolUtils;
import com.topsec.hamming.infra.gateway.aas.dto.Tenant;
import com.topsec.hamming.types.aas.UserType;
import com.topsec.hamming.types.http.RequestConstants;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Base64;

/**
 * <AUTHOR>
 */
@Slf4j
public class HttpHeaderUtils {

  /**
   * 用户名唯一，对应identityName
   * header里包含了这个信息
   *
   * @return
   */
  public static String getUserIdentityName() {
    String creator = "";
    if (RequestContextHolder.getRequestAttributes() != null) {
      creator = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest().getHeader(RequestConstants.USER_NAME.name);
    }
    return Strings.isNullOrEmpty(creator) ? "operator" : creator;
  }

  public static UserType getUserType() {
    if (CIS_MULTI_TENANT_ENABLED && RequestContextHolder.getRequestAttributes() != null) {
      String tenantInfo = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest().getHeader(RequestConstants.TENANT_INFO.name);
      return Strings.isNullOrEmpty(tenantInfo) ? UserType.PLANTFORM_USER : UserType.TENANT_USER;
    }
    return UserType.PLANTFORM_USER;
  }

  public static String getTenantId() {
    if (RequestContextHolder.getRequestAttributes() != null) {
      String tenantInfo = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest().getHeader(RequestConstants.TENANT_INFO.name);
      try {
        Base64.Decoder decoder = Base64.getDecoder();
        Tenant tenant = ToolUtils.OBJECT_MAPPER.readValue(decoder.decode(tenantInfo), Tenant.class);
        return tenant.getTenantId();
      } catch (Exception e) {
        log.error("", e);
      }
    }
    return null;
  }

  public static String getClientPrincipal() {
    if (RequestContextHolder.getRequestAttributes() != null) {
      return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest().getHeader(RequestConstants.CLIENT_PRINCIPAL.name);
    }
    return null;
  }
}
