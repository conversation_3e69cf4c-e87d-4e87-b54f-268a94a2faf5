package com.topsec.hamming.app.dto;

import com.topsec.common.utils.ToolUtils;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
/**
 * 一级平台提供的告警规则-父规则
 */
public class ParentAlarmRule {
  private String id;
  private String cat2;
  private String version;
  private List<String> changelog;
  private List<String> drops;
  private Map<String, Object> fields;
  private List<SubAlarmRule> rules;

  @Override
  public ParentAlarmRule clone() {
    ObjectMapper objectMapper = ToolUtils.OBJECT_MAPPER;
    try {
      return objectMapper.readValue(objectMapper.writeValueAsString(this), ParentAlarmRule.class);
    } catch (Exception e) {
      log.error("", e);
      return new ParentAlarmRule();
    }
  }

}
