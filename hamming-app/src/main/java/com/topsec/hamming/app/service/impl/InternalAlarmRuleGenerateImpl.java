package com.topsec.hamming.app.service.impl;

import com.topsec.hamming.app.config.AlarmRuleConfig;
import com.topsec.hamming.app.service.AlarmRuleGenerator;
import com.topsec.hamming.domain.vo.rule.AlarmRuleReportVo;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service(value = "internalAlarmGenerator")
public class InternalAlarmRuleGenerateImpl implements AlarmRuleGenerator {

  @Autowired
  private AlarmRuleConfig alarmRuleConfig;

  @Override
  public String getFileType() {
    return alarmRuleConfig.getFile().getType().getInternal();
  }

  @Override
  public List<AlarmRuleReportVo> generateAlarmRule(String fileName, String ruleContent) {
    return null;
  }

}
