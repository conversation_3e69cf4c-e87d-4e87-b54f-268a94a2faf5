/*
package com.topsec.hamming.app.tools;

import com.topsec.hamming.types.Expression;
import com.topsec.hamming.infra.utils.ExpressionUtil;

import com.google.common.base.Strings;

*/
/**
 * <AUTHOR>
 *//*

public class ExpressionTools {
  public static String getCondition(Expression.BooleanExpression expression) {
    return Strings.isNullOrEmpty(expression.toString()) ? ExpressionUtil.generateScalarSQL(expression) : expression.toString();
  }
}
*/
